"""
Enhanced Statistics Engine with unified PnL calculation.
This module provides event-driven statistics calculation with consistent PnL data.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, date
import numpy as np
import pandas as pd
from collections import defaultdict

from vnpy_backtester.engines.event_driven_engine import EventDrivenEngine
from vnpy_backtester.utils.event import Event, EventType
from vnpy_backtester.objects.object import TradeData
from vnpy_backtester.utils.base import DailyResult


class StatisticsEngine(EventDrivenEngine):
    """
    事件驱动的统计引擎
    使用统一的PnL管理器确保统计数据一致性
    """
    
    def __init__(self, event_engine=None):
        """初始化统计引擎"""
        super().__init__(event_engine)
        
        # 基础参数
        self.initial_capital: float = 100000.0
        self.annual_days: int = 240
        self.risk_free_rate: float = 0.0
        
        # 交易统计
        self.trades: List[Dict[str, Any]] = []
        self.winning_trades: List[Dict[str, Any]] = []
        self.losing_trades: List[Dict[str, Any]] = []
        
        # 日统计
        self.daily_pnl: Dict[date, float] = {}
        self.daily_balance: Dict[date, float] = {}
        self.daily_returns: Dict[date, float] = {}
        
        # 累计统计
        self.total_pnl: float = 0.0
        self.total_commission: float = 0.0
        self.total_slippage: float = 0.0
        self.total_net_pnl: float = 0.0
        self.current_balance: float = 0.0
        self.max_balance: float = 0.0
        self.max_drawdown: float = 0.0
        
        # 时间统计
        self.start_date: Optional[datetime] = None
        self.end_date: Optional[datetime] = None
        
    def on_init(self):
        """初始化回调"""
        self.write_log("StatisticsEngine initialized")
        
    def on_start(self):
        """启动回调"""
        self.write_log("StatisticsEngine started")
        self.current_balance = self.initial_capital
        self.max_balance = self.initial_capital
        
    def on_stop(self):
        """停止回调"""
        self.write_log("StatisticsEngine stopped")
        
    def on_trade(self, trade: TradeData, pnl_result: Any):
        """交易回调 - 记录交易统计"""
        if not pnl_result:
            return
            
        # 记录交易信息
        trade_info = {
            "datetime": trade.datetime,
            "symbol": trade.vt_symbol,
            "direction": trade.direction.value,
            "offset": trade.offset.value,
            "price": trade.price,
            "volume": trade.volume,
            "pnl": pnl_result.trade_pnl,
            "commission": pnl_result.commission,
            "slippage": pnl_result.slippage,
            "net_pnl": pnl_result.net_pnl,
            "return_rate": pnl_result.return_rate
        }
        
        self.trades.append(trade_info)
        
        # 分类盈利和亏损交易
        if pnl_result.net_pnl > 0:
            self.winning_trades.append(trade_info)
        elif pnl_result.net_pnl < 0:
            self.losing_trades.append(trade_info)
            
        # 更新时间范围
        if not self.start_date or trade.datetime < self.start_date:
            self.start_date = trade.datetime
        if not self.end_date or trade.datetime > self.end_date:
            self.end_date = trade.datetime
            
    def on_pnl_update(self, pnl_data: Dict[str, Any]):
        """盈亏更新回调 - 更新累计统计"""
        total_net_pnl = pnl_data.get("total_net_pnl", 0.0)
        trade = pnl_data.get("trade")
        pnl_result = pnl_data.get("pnl_result")
        
        if trade and pnl_result:
            # 更新累计数据
            self.total_pnl += pnl_result.trade_pnl
            self.total_commission += pnl_result.commission
            self.total_slippage += pnl_result.slippage
            self.total_net_pnl = total_net_pnl
            
            # 更新余额
            self.current_balance = self.initial_capital + total_net_pnl
            self.max_balance = max(self.max_balance, self.current_balance)
            
            # 计算回撤
            current_drawdown = (self.max_balance - self.current_balance) / self.max_balance * 100 if self.max_balance > 0 else 0.0
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
            
            # 更新日统计
            trade_date = trade.datetime.date()
            self.daily_pnl[trade_date] = self.daily_pnl.get(trade_date, 0.0) + pnl_result.net_pnl
            self.daily_balance[trade_date] = self.current_balance
            
            # 计算日收益率
            if trade_date in self.daily_balance:
                prev_balance = self.initial_capital
                # 找到前一天的余额
                for d in sorted(self.daily_balance.keys()):
                    if d < trade_date:
                        prev_balance = self.daily_balance[d]
                    elif d == trade_date:
                        break
                        
                if prev_balance > 0:
                    self.daily_returns[trade_date] = (self.daily_balance[trade_date] - prev_balance) / prev_balance
                    
    def set_parameters(self, initial_capital: float = None, annual_days: int = None, risk_free_rate: float = None):
        """设置统计参数"""
        if initial_capital is not None:
            self.initial_capital = initial_capital
            self.current_balance = initial_capital
            self.max_balance = initial_capital
        if annual_days is not None:
            self.annual_days = annual_days
        if risk_free_rate is not None:
            self.risk_free_rate = risk_free_rate
            
    def calculate_statistics(self) -> Dict[str, Any]:
        """计算完整的统计指标"""
        if not self.trades:
            return self._get_empty_statistics()
            
        # 基础统计
        total_trades = len(self.trades)
        winning_trades = len(self.winning_trades)
        losing_trades = len(self.losing_trades)
        
        # 胜率
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0.0
        
        # 平均盈利和亏损
        avg_winning = np.mean([t["net_pnl"] for t in self.winning_trades]) if self.winning_trades else 0.0
        avg_losing = np.mean([t["net_pnl"] for t in self.losing_trades]) if self.losing_trades else 0.0
        
        # 盈亏比
        profit_ratio = abs(avg_winning / avg_losing) if avg_losing != 0 else float('inf')
        
        # 利润因子
        total_profit = sum(t["net_pnl"] for t in self.winning_trades)
        total_loss = abs(sum(t["net_pnl"] for t in self.losing_trades))
        profit_factor = (total_profit / total_loss) if total_loss > 0 else float('inf')
        
        # 时间统计
        total_days = (self.end_date - self.start_date).days if self.start_date and self.end_date else 0
        profit_days = len([pnl for pnl in self.daily_pnl.values() if pnl > 0])
        loss_days = len([pnl for pnl in self.daily_pnl.values() if pnl < 0])
        
        # 收益率统计
        total_return = (self.current_balance - self.initial_capital) / self.initial_capital * 100
        annual_return = (total_return / total_days * self.annual_days) if total_days > 0 else 0.0
        
        # 夏普比率
        if self.daily_returns:
            daily_return_series = pd.Series(list(self.daily_returns.values()))
            return_std = daily_return_series.std() * np.sqrt(self.annual_days)
            excess_return = annual_return - self.risk_free_rate
            sharpe_ratio = (excess_return / return_std) if return_std > 0 else 0.0
        else:
            sharpe_ratio = 0.0
            
        # 最大连续盈利和亏损
        max_consecutive_wins, max_consecutive_losses = self._calculate_consecutive_stats()
        
        return {
            # 基础信息
            "start_date": self.start_date,
            "end_date": self.end_date,
            "total_days": total_days,
            "capital": self.initial_capital,
            "end_balance": self.current_balance,
            
            # 收益统计
            "total_return": total_return,
            "annual_return": annual_return,
            "max_drawdown": self.max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            
            # 交易统计
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "profit_ratio": profit_ratio,
            "profit_factor": profit_factor,
            
            # 盈亏统计
            "total_pnl": self.total_pnl,
            "total_commission": self.total_commission,
            "total_slippage": self.total_slippage,
            "total_net_pnl": self.total_net_pnl,
            "avg_winning": avg_winning,
            "avg_losing": avg_losing,
            
            # 日统计
            "profit_days": profit_days,
            "loss_days": loss_days,
            "max_consecutive_wins": max_consecutive_wins,
            "max_consecutive_losses": max_consecutive_losses,
        }
        
    def _get_empty_statistics(self) -> Dict[str, Any]:
        """获取空统计数据"""
        return {
            "start_date": None,
            "end_date": None,
            "total_days": 0,
            "capital": self.initial_capital,
            "end_balance": self.initial_capital,
            "total_return": 0.0,
            "annual_return": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "win_rate": 0.0,
            "profit_ratio": 0.0,
            "profit_factor": 0.0,
            "total_pnl": 0.0,
            "total_commission": 0.0,
            "total_slippage": 0.0,
            "total_net_pnl": 0.0,
            "avg_winning": 0.0,
            "avg_losing": 0.0,
            "profit_days": 0,
            "loss_days": 0,
            "max_consecutive_wins": 0,
            "max_consecutive_losses": 0,
        }
        
    def _calculate_consecutive_stats(self) -> tuple:
        """计算最大连续盈利和亏损次数"""
        if not self.trades:
            return 0, 0
            
        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0
        
        for trade in self.trades:
            if trade["net_pnl"] > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            elif trade["net_pnl"] < 0:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
            else:
                current_wins = 0
                current_losses = 0
                
        return max_wins, max_losses
        
    def get_trade_summary(self) -> Dict[str, Any]:
        """获取交易汇总"""
        return {
            "total_trades": len(self.trades),
            "winning_trades": len(self.winning_trades),
            "losing_trades": len(self.losing_trades),
            "trades_data": self.trades,
            "winning_trades_data": self.winning_trades,
            "losing_trades_data": self.losing_trades
        }
        
    def reset_statistics(self):
        """重置所有统计数据"""
        self.trades.clear()
        self.winning_trades.clear()
        self.losing_trades.clear()
        self.daily_pnl.clear()
        self.daily_balance.clear()
        self.daily_returns.clear()
        
        self.total_pnl = 0.0
        self.total_commission = 0.0
        self.total_slippage = 0.0
        self.total_net_pnl = 0.0
        self.current_balance = self.initial_capital
        self.max_balance = self.initial_capital
        self.max_drawdown = 0.0
        
        self.start_date = None
        self.end_date = None
        
    def write_log(self, msg: str):
        """写日志"""
        print(f"[StatisticsEngine] {msg}")
