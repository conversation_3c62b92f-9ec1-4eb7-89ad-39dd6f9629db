"""
MA交叉策略 - 当MA10上穿MA20时反手做多，下穿时反手做空
不使用固定持仓K线数量，而是在出现反向信号时平仓并反向开仓
"""

from typing import List
import numpy as np
from datetime import datetime

from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.log_engine import log_engine
from vnpy_backtester.utils.array_manager import ArrayManager


class Position:
    """
    持仓类 - 用于记录每个持仓的信息
    """

    def __init__(self, direction: str, price: float, volume: float, entry_time: datetime):
        """
        初始化持仓

        参数:
        direction: 方向，"long"或"short"
        price: 开仓价格
        volume: 开仓数量
        entry_time: 开仓时间
        """
        self.direction = direction  # "long"或"short"
        self.price = price  # 开仓价格
        self.volume = volume  # 开仓数量
        self.entry_time = entry_time  # 开仓时间


class MACrossPositionStrategy(StrategyTemplate):
    """
    MA交叉策略

    策略逻辑：
    1. 当短期均线(MA10)上穿长期均线(MA20)时反手做多
    2. 当短期均线(MA10)下穿长期均线(MA20)时反手做空
    3. 不使用固定持仓K线数量，而是在出现反向信号时平仓并反向开仓
    """

    # 策略参数
    fast_window = 10  # 快速均线窗口
    slow_window = 20  # 慢速均线窗口
    position_size = 10  # 每次开仓的数量或金额
    size = 1  # 合约乘数
    commission_rate = 0.0003  # 默认手续费率，将从引擎获取
    slippage_rate = 0.001  # 默认滑点率，将从引擎获取
    order_type = "quantity"  # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)

    # 策略变量
    fast_ma = 0.0
    slow_ma = 0.0
    positions = []  # 持仓列表，每个元素是一个Position对象

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.positions = []  # 初始化持仓列表
        self.current_pos_direction = ""  # 当前持仓方向，"long"或"short"或空字符串
        self.debug_mode = False  # 调试模式，默认关闭

        # 从设置中获取参数
        if "fast_window" in self.setting:
            self.fast_window = self.setting["fast_window"]
        if "slow_window" in self.setting:
            self.slow_window = self.setting["slow_window"]
        if "position_size" in self.setting:
            self.position_size = self.setting["position_size"]
        if "debug_mode" in self.setting:
            self.debug_mode = self.setting["debug_mode"]
        if "order_type" in self.setting:
            self.order_type = self.setting["order_type"]
            self.write_log(f"设置下单方式: {self.order_type}")

        # 记录下单方式和仓位大小
        if self.order_type == "quantity":
            self.write_log(f"设置每次开仓数量: {self.position_size}")
        else:  # order_type == "amount"
            self.write_log(f"设置每次开仓金额: {self.position_size}")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

        # 创建K线管理器，大小为慢速均线窗口的1.5倍，确保有足够的数据计算均线和判断交叉
        # 但不要过大，避免数据不足时计算短期均线出现问题
        self.am = ArrayManager(size=max(int(self.slow_window * 1.5), 100))

        # 记录上一根K线的均线值，用于判断交叉
        self.last_fast_ma = 0.0
        self.last_slow_ma = 0.0

    def on_init(self):
        """
        策略初始化
        """
        self.write_log("MA交叉策略初始化")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

    def on_start(self):
        """
        策略启动
        """
        self.write_log("MA交叉策略启动")

    def on_stop(self):
        """
        策略停止
        """
        self.write_log("MA交叉策略停止")

    def write_log(self, msg, level="INFO"):
        """
        写入日志

        参数:
        msg: 日志消息
        level: 日志级别，可以是"DEBUG", "INFO", "WARNING", "ERROR"
        """
        # 只有在非DEBUG级别或者DEBUG模式开启时才记录日志
        if level != "DEBUG" or getattr(self, "debug_mode", False):
            # 使用日志引擎写入日志
            log_engine.write_log(f"{self.strategy_name} - {msg}")

            # 同时调用父类的write_log方法
            super().write_log(msg)

    def get_available_capital(self, force_recalculate=False):
        """
        获取可用资金

        参数:
        force_recalculate: 是否强制重新计算，不使用缓存

        返回:
        float: 可用资金
        """
        # 缓存计算结果，避免频繁重复计算
        if not force_recalculate and hasattr(self, '_last_bar_datetime') and self._last_bar_datetime == self.bar.datetime:
            return self._cached_available_capital

        # 从引擎获取初始资金
        initial_capital = 50000.0  # 默认值
        if hasattr(self, "engine") and hasattr(self.engine, "capital"):
            initial_capital = self.engine.capital

        # 使用累计方式计算已使用资金，而不是每次都重新计算
        if not hasattr(self, '_used_capital'):
            # 首次计算
            self._used_capital = 0.0
            for position in self.positions:
                self._used_capital += position.price * position.volume

        # 计算可用资金
        available_capital = initial_capital - self._used_capital

        # 确保可用资金不小于0
        available_capital = max(0.0, available_capital)

        # 缓存计算结果
        self._last_bar_datetime = self.bar.datetime
        self._cached_available_capital = available_capital

        return available_capital

    def on_bar(self, bar: BarData):
        """
        K线更新回调
        """
        # 保存当前K线对象，供其他方法使用
        self.bar = bar

        # 更新K线管理器
        self.am.update_bar(bar.open_price, bar.high_price,
                           bar.low_price, bar.close_price, bar.volume)

        # 如果数据不足，则不进行交易
        if not self.am.inited:
            self.write_log(
                f"数据不足，无法计算均线：{bar.datetime}, 数据点数量：{self.am.count}/{self.slow_window}", level="DEBUG")
            return

        # 保存上一次的均线值
        self.last_fast_ma = self.fast_ma
        self.last_slow_ma = self.slow_ma

        # 计算当前的均线值
        fast_ma_array = self.am.get_ma(self.fast_window)
        slow_ma_array = self.am.get_ma(self.slow_window)
        self.fast_ma = fast_ma_array[-1]
        self.slow_ma = slow_ma_array[-1]

        # 添加调试日志，帮助理解均线计算
        self.write_log(
            f"均线计算 - 当前K线：{bar.datetime}, 价格：{bar.close_price}, 快线：{self.fast_ma:.2f}, 慢线：{self.slow_ma:.2f}, 上一根快线：{self.last_fast_ma:.2f}, 上一根慢线：{self.last_slow_ma:.2f}",
            level="DEBUG")

        # 交易信号
        # 添加一个小的阈值，避免由于浮点数精度问题导致的假信号
        threshold = 0.0001

        # 确保有足够的数据进行交叉判断
        if self.am.inited and self.last_fast_ma != 0 and self.last_slow_ma != 0:
            # 金叉条件：上一根K线快线在慢线下方，当前K线快线在慢线上方，且差值大于阈值
            cross_over_condition1 = self.last_fast_ma < self.last_slow_ma
            cross_over_condition2 = self.fast_ma > self.slow_ma
            cross_over_condition3 = abs(
                self.fast_ma - self.slow_ma) > threshold
            cross_over = cross_over_condition1 and cross_over_condition2 and cross_over_condition3

            # 死叉条件：上一根K线快线在慢线上方，当前K线快线在慢线下方，且差值大于阈值
            cross_below_condition1 = self.last_fast_ma > self.last_slow_ma
            cross_below_condition2 = self.fast_ma < self.slow_ma
            cross_below_condition3 = abs(
                self.fast_ma - self.slow_ma) > threshold
            cross_below = cross_below_condition1 and cross_below_condition2 and cross_below_condition3

        else:
            # 数据不足，不产生交叉信号
            cross_over = False
            cross_below = False
            self.write_log(f"数据不足，无法判断交叉信号：{bar.datetime}", level="DEBUG")

        # 添加调试日志，帮助理解交叉信号
        if cross_over:
            self.write_log(f"检测到金叉信号：{bar.datetime}", level="DEBUG")
        elif cross_below:
            self.write_log(f"检测到死叉信号：{bar.datetime}", level="DEBUG")

        # 计算当前总持仓量和方向
        total_long = 0
        total_short = 0

        for position in self.positions:
            if position.direction == "long":
                total_long += position.volume
            else:  # direction == "short"
                total_short += position.volume

        # 更新当前持仓方向
        if total_long > 0 and total_short == 0:
            self.current_pos_direction = "long"
        elif total_short > 0 and total_long == 0:
            self.current_pos_direction = "short"
        elif total_long == 0 and total_short == 0:
            self.current_pos_direction = ""
        else:
            # 同时有多空持仓，这种情况不应该出现
            self.write_log(
                f"警告：同时存在多空持仓，多头：{total_long}，空头：{total_short}", level="WARNING")

        # 获取当前可用资金
        available_capital = self.get_available_capital()

        # 计算开仓数量和所需资金
        margin_rate = 1.0  # 全额保证金

        if self.order_type == "quantity":
            # 按币数量下单
            volume = self.position_size
            # 计算开仓所需资金
            required_capital = bar.close_price * volume * margin_rate
            # 计算手续费
            commission = required_capital * self.commission_rate
            # 计算滑点 - 使用固定点数方式
            slippage = volume * self.size * self.slippage_rate
        else:  # order_type == "amount"
            # 按金额下单
            amount = self.position_size
            # 计算可以买入的币数量（考虑手续费和滑点）
            # 解方程：price * volume + price * volume * commission_rate + volume * slippage_rate = amount
            # 简化为：volume * price * (1 + commission_rate) + volume * slippage_rate = amount
            # 得到：volume = amount / (price * (1 + commission_rate) + slippage_rate)
            volume = amount / (bar.close_price *
                               (1 + self.commission_rate) + self.slippage_rate)
            # 四舍五入到4位小数
            volume = round(volume, 4)
            # 计算实际所需资金
            required_capital = amount
            # 计算手续费和滑点
            commission = bar.close_price * volume * self.commission_rate
            slippage = volume * self.size * self.slippage_rate

        # 加上手续费和滑点
        total_cost = required_capital + commission + slippage

        # 检查是否接近回测结束
        # 获取剩余K线数量（由引擎设置在bar对象中）
        remaining_bars = getattr(bar, 'remaining_bars', 0)

        # 如果剩余K线数量少于10，不开新仓
        if remaining_bars < 10:
            return  # 直接返回，不执行开仓逻辑

        # 金叉信号处理
        if cross_over:
            # 只有当前没有多仓或者有空仓时才处理金叉信号
            if self.current_pos_direction != "long":
                # 如果当前持有空仓，先平仓
                if self.current_pos_direction == "short":
                    # 计算总空头持仓量
                    total_short_volume = sum(
                        p.volume for p in self.positions if p.direction == "short")

                    # 计算平仓盈亏
                    avg_short_price = sum(
                        p.price * p.volume for p in self.positions if p.direction == "short") / total_short_volume
                    profit = (avg_short_price - bar.close_price) * \
                        total_short_volume
                    profit_pct = (avg_short_price / bar.close_price - 1) * 100

                    # 计算平仓时的手续费和滑点
                    close_commission = total_short_volume * self.size * \
                        bar.close_price * self.commission_rate

                    # 滑点计算 - 使用固定点数方式
                    close_slippage = total_short_volume * self.size * self.slippage_rate  # 固定点数方式

                    # 计算开仓时的手续费和滑点（回溯计算）
                    open_commission = total_short_volume * self.size * \
                        avg_short_price * self.commission_rate

                    # 滑点计算 - 使用固定点数方式
                    open_slippage = total_short_volume * self.size * self.slippage_rate  # 固定点数方式

                    # 计算总手续费和总滑点
                    total_commission = open_commission + close_commission
                    total_slippage = open_slippage + close_slippage

                    # 计算净盈亏（包含开仓和平仓的所有成本）
                    net_profit = profit - total_commission - total_slippage

                    # 平仓
                    self.cover(bar.close_price, total_short_volume)

                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital -= avg_short_price * total_short_volume
                        # 清除资金缓存，确保下次获取可用资金时重新计算
                        if hasattr(self, '_cached_available_capital'):
                            delattr(self, '_cached_available_capital')

                    # 记录平仓信息
                    self.write_log(
                        f"金叉平空：{bar.datetime}, 价格：{bar.close_price}, 数量：{total_short_volume}, 平均开仓价：{avg_short_price:.2f}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

                    # 清空持仓列表
                    self.positions = [
                        p for p in self.positions if p.direction != "short"]
                    self.current_pos_direction = ""

                    # 立即更新可用资金，确保后续操作使用最新的资金数据
                    available_capital = self.get_available_capital(
                        force_recalculate=True)

                # 开多仓
                if available_capital >= total_cost:
                    # 资金充足，可以开仓
                    self.buy(bar.close_price, volume)
                    # 创建新的持仓对象并添加到持仓列表
                    new_position = Position(
                        "long", bar.close_price, volume, bar.datetime)
                    self.positions.append(new_position)
                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital += bar.close_price * volume

                    # 记录日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"金叉做多(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
                    else:
                        self.write_log(
                            f"金叉做多(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
                    self.current_pos_direction = "long"
                else:
                    # 资金不足，记录到日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                    else:
                        self.write_log(
                            f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
            else:
                # 已经有多仓，忽略金叉信号
                self.write_log(f"已有多仓，忽略金叉信号：{bar.datetime}", level="DEBUG")

        # 死叉信号处理
        elif cross_below:
            # 只有当前没有空仓或者有多仓时才处理死叉信号
            if self.current_pos_direction != "short":
                # 如果当前持有多仓，先平仓
                if self.current_pos_direction == "long":
                    # 计算总多头持仓量
                    total_long_volume = sum(
                        p.volume for p in self.positions if p.direction == "long")

                    # 计算平仓盈亏
                    avg_long_price = sum(
                        p.price * p.volume for p in self.positions if p.direction == "long") / total_long_volume
                    profit = (bar.close_price - avg_long_price) * \
                        total_long_volume
                    profit_pct = (bar.close_price / avg_long_price - 1) * 100

                    # 计算平仓时的手续费和滑点
                    close_commission = total_long_volume * self.size * \
                        bar.close_price * self.commission_rate

                    # 滑点计算 - 使用固定点数方式
                    close_slippage = total_long_volume * self.size * self.slippage_rate  # 固定点数方式

                    # 计算开仓时的手续费和滑点（回溯计算）
                    open_commission = total_long_volume * self.size * \
                        avg_long_price * self.commission_rate

                    # 滑点计算 - 使用固定点数方式
                    open_slippage = total_long_volume * self.size * self.slippage_rate  # 固定点数方式

                    # 计算总手续费和总滑点
                    total_commission = open_commission + close_commission
                    total_slippage = open_slippage + close_slippage

                    # 计算净盈亏（包含开仓和平仓的所有成本）
                    net_profit = profit - total_commission - total_slippage

                    # 平仓
                    self.sell(bar.close_price, total_long_volume)

                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital -= avg_long_price * total_long_volume
                        # 清除资金缓存，确保下次获取可用资金时重新计算
                        if hasattr(self, '_cached_available_capital'):
                            delattr(self, '_cached_available_capital')

                    # 记录平仓信息
                    self.write_log(
                        f"死叉平多：{bar.datetime}, 价格：{bar.close_price}, 数量：{total_long_volume}, 平均开仓价：{avg_long_price:.2f}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

                    # 清空持仓列表
                    self.positions = [
                        p for p in self.positions if p.direction != "long"]
                    self.current_pos_direction = ""

                    # 立即更新可用资金，确保后续操作使用最新的资金数据
                    available_capital = self.get_available_capital(
                        force_recalculate=True)

                # 开空仓
                if available_capital >= total_cost:
                    # 资金充足，可以开仓
                    self.short(bar.close_price, volume)
                    # 创建新的持仓对象并添加到持仓列表
                    new_position = Position(
                        "short", bar.close_price, volume, bar.datetime)
                    self.positions.append(new_position)
                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital += bar.close_price * volume

                    # 记录日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"死叉做空(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
                    else:
                        self.write_log(
                            f"死叉做空(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
                    self.current_pos_direction = "short"
                else:
                    # 资金不足，记录到日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                    else:
                        self.write_log(
                            f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
            else:
                # 已经有空仓，忽略死叉信号
                self.write_log(f"已有空仓，忽略死叉信号：{bar.datetime}", level="DEBUG")

        # 保存当前K线，供下一次计算使用
        self.last_bar = bar
