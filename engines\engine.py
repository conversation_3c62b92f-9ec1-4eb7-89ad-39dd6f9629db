"""
Main backtesting engine with event-driven architecture
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Type, Callable, Any
import traceback
import numpy as np
import pandas as pd
# 使用字典代替defaultdict，提高性能

from vnpy_backtester.utils.constant import Direction, Offset, Exchange, Interval, Status, BacktestingMode, EngineType
from vnpy_backtester.objects.object import OrderData, TradeData, BarData, TickData
from vnpy_backtester.utils.base import StopOrder, StopOrderStatus, DailyResult, TradeResult, BacktestingResult, STOPORDER_PREFIX
from vnpy_backtester.utils.utility import extract_vt_symbol, round_to, calculate_statistics
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.utils.event import Event, EventEngine, EventType
from vnpy_backtester.engines.event_driven_engine import EventDrivenEngine
from vnpy_backtester.utils.pnl_manager import PnLManager


class BacktestingEngine(EventDrivenEngine):
    """
    Enhanced backtesting engine with event-driven architecture.
    Inherits from EventDrivenEngine for unified event handling.
    """

    engine_type: EngineType = EngineType.BACKTESTING
    gateway_name: str = "BACKTESTING"

    def __init__(self, event_engine: EventEngine = None) -> None:
        """
        Initialize backtesting engine with event-driven architecture.
        """
        # 初始化父类
        super().__init__(event_engine)

        # 回测参数
        self.vt_symbol: str = ""
        self.symbol: str = ""
        self.exchange: Exchange = None
        self.start: datetime = None
        self.end: datetime = None
        self.rate: float = 0
        self.slippage: float = 0
        self.size: float = 1
        self.pricetick: float = 0
        self.capital: int = 1_000_000
        self.risk_free: float = 0
        self.annual_days: int = 240
        self.mode: BacktestingMode = BacktestingMode.BAR
        self.holding_bars: int = 96  # 默认持仓K线数量
        self.max_positions: Optional[int] = None  # 最大持仓数限制，None表示无限制

        # 初始化交易日志引擎
        from vnpy_backtester.utils.trade_log_engine import initialize_trade_log_engine
        self.trade_log_engine = initialize_trade_log_engine(self.event_engine)

        # 设置PnL管理器参数
        self.pnl_manager.set_parameters(
            commission_rate=self.rate,
            slippage_rate=self.slippage,
            size=self.size
        )

    # 实现EventDrivenEngine的抽象方法
    def on_init(self):
        """初始化回调"""
        self.write_log("BacktestingEngine initialized")

    def on_start(self):
        """启动回调"""
        self.write_log("BacktestingEngine started")

    def on_stop(self):
        """停止回调"""
        self.write_log("BacktestingEngine stopped")

    def on_trade(self, trade: TradeData, pnl_result: Any):
        """交易回调 - 使用统一的PnL计算"""
        # 这里可以添加额外的交易处理逻辑
        pass

    def on_pnl_update(self, pnl_data: Dict[str, Any]):
        """盈亏更新回调"""
        # 可以在这里处理盈亏更新事件
        pass

        # 设置交易日志引擎参数
        self.trade_log_engine.set_parameters(
            commission_rate=self.rate,
            slippage_rate=self.slippage,
            size=self.size
        )

        self.strategy_class: Type[StrategyTemplate] = None
        self.strategy: StrategyTemplate = None
        self.tick: TickData = None
        self.bar: BarData = None
        self.datetime: datetime = None

        self.interval: Interval = None
        self.days: int = 0
        self.callback: Callable = None
        self.history_data: List = []

        self.limit_order_count: int = 0
        self.limit_orders: Dict[str, OrderData] = {}
        self.active_limit_orders: Dict[str, OrderData] = {}

        self.stop_order_count: int = 0
        self.stop_orders: Dict[str, StopOrder] = {}
        self.active_stop_orders: Dict[str, StopOrder] = {}

        self.trade_count: int = 0
        self.trades: Dict[str, TradeData] = {}

        self.logs: List[str] = []

        self.daily_results: Dict[datetime, DailyResult] = {}
        self.daily_df: pd.DataFrame = None

    def set_parameters(
        self,
        vt_symbol: str,
        start: datetime,
        rate: float,
        slippage: float,
        capital: int = 0,
        end: datetime = None,
        mode: BacktestingMode = BacktestingMode.BAR,
        risk_free: float = 0,
        annual_days: int = 240,
        interval: Interval = None,
        size: float = 1,
        pricetick: float = 0.01,
        holding_bars: int = 96,
        position_size: int = 1,
        max_positions: Optional[int] = None,  # 最大持仓数限制
        **strategy_params  # 添加额外的策略参数
    ) -> None:
        """
        Set parameters for backtesting.

        Parameters:
            vt_symbol: Symbol with exchange name (format: "symbol.exchange").
            start: Start date of backtesting.
            rate: Commission rate.
            slippage: Slippage setting.
            capital: Initial capital for backtesting.
            end: End date of backtesting.
            mode: Backtesting mode (BAR or TICK).
            risk_free: Risk-free rate for calculating Sharpe ratio.
            annual_days: Annual trading days for calculating Sharpe ratio.
            interval: Bar interval for backtesting.
            size: Contract size.
            pricetick: Price tick size.
            holding_bars: Number of bars to hold a position.
            position_size: Size of each position.
            max_positions: Maximum number of positions allowed (None for unlimited).
            **strategy_params: Additional parameters for the strategy.
        """
        self.mode = mode
        self.vt_symbol = vt_symbol
        self.interval = Interval.DAILY if interval is None else Interval(
            interval)
        self.rate = rate
        self.slippage = slippage
        self.size = size
        self.pricetick = pricetick
        self.start = start

        self.symbol, exchange_str = self.vt_symbol.split(".")
        self.exchange = Exchange(exchange_str)

        self.capital = capital

        # 更新交易日志引擎参数
        if hasattr(self, 'trade_log_engine'):
            self.trade_log_engine.set_parameters(
                commission_rate=rate,
                slippage_rate=slippage,
                size=size
            )

        if not end:
            end = datetime.now()
        self.end = end.replace(hour=23, minute=59, second=59)

        self.mode = mode
        self.risk_free = risk_free
        self.annual_days = annual_days
        self.holding_bars = holding_bars
        self.position_size = position_size
        self.max_positions = max_positions

        # 保存额外的策略参数
        self.strategy_params = {
            "holding_bars": holding_bars,
            "position_size": position_size,
            "max_positions": max_positions
        }

        # 添加其他策略参数
        if strategy_params:
            self.strategy_params.update(strategy_params)

    def add_strategy(self, strategy_class: Type[StrategyTemplate]) -> None:
        """
        Add strategy for backtesting.

        Parameters:
            strategy_class: Strategy class to be added.
        """
        self.strategy_class = strategy_class

        # 创建策略参数字典
        strategy_setting = self.strategy_params if hasattr(self, "strategy_params") else {
            "holding_bars": self.holding_bars}

        # 创建策略实例
        self.strategy = strategy_class(
            self, strategy_class.__name__, self.vt_symbol, strategy_setting
        )

    def load_data(self) -> None:
        """
        Load historical data for backtesting.
        """
        self.output("Start loading historical data")

        if not self.end:
            self.end = datetime.now()

        if self.start >= self.end:
            self.output("Start date should be earlier than end date")
            return

    def run_backtesting(self) -> None:
        """
        Run backtesting with optimized performance using event-driven architecture.
        """
        if self.mode == BacktestingMode.BAR:
            func = self.new_bar
        else:
            func = self.new_tick

        # 注册事件处理函数
        self.register_event()

        # 在回测环境中，不需要启动事件引擎线程，因为我们直接处理事件
        # self.event_engine.start()

        # 发送初始化事件
        self.event_engine.put(Event(EventType.EVENT_INIT))
        self.strategy.inited = True
        self.output("Strategy initialized")

        # 发送启动事件
        self.event_engine.put(Event(EventType.EVENT_START))
        self.strategy.trading = True
        self.output("Start backtesting")

        # 预处理：提前创建所有需要的对象和映射
        total_size: int = len(self.history_data)

        # 使用较小的批处理大小，确保能够显示每10%的进度
        batch_size: int = max(int(total_size / 20), 100)

        # 预先分配内存，避免动态扩展
        self.daily_results = {}

        # 创建datetime到索引的映射，使用字典推导式，避免在每个K线中重复计算
        # 使用更高效的哈希表实现
        self._datetime_to_index_map = {}
        for i, hist_bar in enumerate(self.history_data):
            self._datetime_to_index_map[hist_bar.datetime] = i
        self._total_bars = total_size

        # 预先计算每个K线的剩余K线数量，使用numpy向量化操作
        remaining_bars = np.arange(total_size-1, -1, -1)

        # 使用批量处理，减少循环开销
        bars_per_batch = 1000
        for i in range(0, total_size, bars_per_batch):
            end_idx = min(i + bars_per_batch, total_size)
            for j in range(i, end_idx):
                self.history_data[j].remaining_bars = remaining_bars[j]

        # 预先创建日期对象，减少日期转换开销
        # 使用更高效的集合操作
        date_set = set()
        for bar in self.history_data:
            date_set.add(bar.datetime.date())

        # 一次性创建所有日期结果对象
        for d in date_set:
            self.daily_results[d] = None

        # 使用批处理方式处理数据
        # 设置进度显示，每10%更新一次
        last_progress_percent = 0

        try:
            # 使用单一的try-except包裹整个处理过程，减少异常处理开销
            for i in range(0, total_size, batch_size):
                batch_end = min(i + batch_size, total_size)
                batch_data: list = self.history_data[i:batch_end]

                # 处理批次中的每个K线
                for data in batch_data:
                    func(data)

                # 计算当前进度百分比
                current_progress = batch_end
                current_percent = int((current_progress / total_size) * 100)

                # 计算当前应该显示的10%整数倍进度点
                display_percent = (current_percent // 10) * 10

                # 如果是最后一批，设置为100%
                if current_progress >= total_size:
                    display_percent = 100

                # 只有当进度百分比变化时才显示
                if display_percent > last_progress_percent:
                    last_progress_percent = display_percent

                    # 使用display_percent计算进度条
                    progress_bar_len = display_percent // 10
                    progress_bar: str = "=" * progress_bar_len
                    self.output(
                        f"Progress: {progress_bar} [{display_percent}%]")
        except Exception:
            self.output("Backtesting exception")
            self.output(traceback.format_exc())
            return

        # 发送停止事件
        self.event_engine.put(Event(EventType.EVENT_STOP))
        self.output("Backtesting finished")

    def calculate_result(self) -> None:
        """
        Calculate backtesting result with optimized performance.
        """
        self.output("Start calculating result")

        if not self.trades:
            self.output("No trade generated")
            return

        # 预先创建日期到交易的映射，减少循环次数
        # 使用更高效的哈希表实现
        trade_date_map = {}
        for trade in self.trades.values():
            d = trade.datetime.date()
            if d not in trade_date_map:
                trade_date_map[d] = []
            trade_date_map[d].append(trade)

        # 批量处理每个日期的交易 - 使用更高效的批处理
        # 预先分配所有日期结果对象
        for d in trade_date_map:
            if d not in self.daily_results or self.daily_results[d] is None:
                self.daily_results[d] = DailyResult(d, self.close_price)

        # 批量添加交易，使用内存视图避免复制
        for d, trades in trade_date_map.items():
            daily_result = self.daily_results[d]
            # 使用extend一次性添加所有交易，而不是逐个添加
            daily_result.trade_list.extend(trades)
            daily_result.trade_count += len(trades)

        # 按日期排序，确保正确的计算顺序
        # 使用列表推导式和排序，避免多次查找
        sorted_dates = sorted(self.daily_results.keys())
        sorted_daily_results = [self.daily_results[d] for d in sorted_dates]

        # 预分配结果数组，避免动态扩展
        num_days = len(sorted_dates)
        results = {
            "date": sorted_dates,
            "close_price": np.zeros(num_days),
            "net_pnl": np.zeros(num_days),
            "holding_pnl": np.zeros(num_days),
            "trading_pnl": np.zeros(num_days),
            "commission": np.zeros(num_days),
            "slippage": np.zeros(num_days),
            "start_pos": np.zeros(num_days),
            "end_pos": np.zeros(num_days),
            "trade_count": np.zeros(num_days, dtype=int),
        }

        # 使用向量化操作计算每日结果
        pre_close = 0
        start_pos = 0

        # 使用批处理计算每日结果，减少循环开销
        batch_size = 100  # 每批处理的日期数量
        for batch_start in range(0, num_days, batch_size):
            batch_end = min(batch_start + batch_size, num_days)
            batch_indices = range(batch_start, batch_end)

            # 处理每个批次中的日期
            for i in batch_indices:
                daily_result = sorted_daily_results[i]

                # 计算每日盈亏
                daily_result.calculate_pnl(
                    pre_close,
                    start_pos,
                    self.size,
                    self.rate,
                    self.slippage
                )

                # 更新下一次迭代的值
                pre_close = daily_result.close_price
                start_pos = daily_result.end_pos

                # 直接填充结果数组，避免额外的循环
                results["close_price"][i] = daily_result.close_price
                results["net_pnl"][i] = daily_result.net_pnl
                results["holding_pnl"][i] = daily_result.holding_pnl
                results["trading_pnl"][i] = daily_result.trading_pnl
                results["commission"][i] = daily_result.commission
                results["slippage"][i] = daily_result.slippage
                results["start_pos"][i] = daily_result.start_pos
                results["end_pos"][i] = daily_result.end_pos
                results["trade_count"][i] = daily_result.trade_count

        # 直接创建DataFrame，避免使用defaultdict和额外的循环
        # 使用更高效的pandas构造函数
        self.daily_df = pd.DataFrame(results).set_index("date")

        self.output("Result calculation finished")
        return self.daily_df

    def calculate_statistics(self, output=True) -> Dict:
        """
        Calculate statistics data.
        """
        self.output("Start calculating statistics")

        # Check if result data is calculated
        if self.daily_df is None or self.daily_df.empty:
            self.calculate_result()

        # Calculate statistics
        statistics = calculate_statistics(
            self.daily_results,
            self.capital,
            self.annual_days,
            engine=self  # 传入引擎参数
        )

        # Output
        if output:
            self.output("-" * 30)
            self.output(f"Start date: {statistics['start_date']}")
            self.output(f"End date: {statistics['end_date']}")
            self.output(f"Total days: {statistics['total_days']}")
            self.output(f"Profit days: {statistics['profit_days']}")
            self.output(f"Loss days: {statistics['loss_days']}")
            self.output(f"Capital: {statistics['capital']:,.2f}")
            self.output(f"End balance: {statistics['end_balance']:,.2f}")
            self.output(f"Total return: {statistics['total_return']:,.2f}%")
            self.output(f"Annual return: {statistics['annual_return']:,.2f}%")
            self.output(f"Max drawdown: {statistics['max_drawdown']:,.2f}%")
            self.output(
                f"Max drawdown percent: {statistics['max_ddpercent']:,.2f}%")
            self.output(f"Sharpe ratio: {statistics['sharpe_ratio']:,.2f}")

            # 输出交易统计
            if 'total_trades' in statistics:
                self.output("-" * 30)
                self.output(f"Total trades: {statistics['total_trades']}")
                self.output(f"Winning trades: {statistics['winning_trades']}")
                self.output(f"Losing trades: {statistics['losing_trades']}")
                self.output(f"Win rate: {statistics['win_rate']:,.2f}%")

                # 显示盈亏比和利润因子
                if 'profit_ratio' in statistics:
                    self.output(
                        # 盈亏比
                        f"Profit ratio: {statistics['profit_ratio']:,.2f}")
                self.output(
                    # 利润因子
                    f"Profit factor: {statistics['profit_factor']:,.2f}")

                # 显示期望收益
                if 'expected_return' in statistics:
                    self.output(
                        f"Expected return: {statistics['expected_return']:,.2f}")

                # 显示平均盈利和平均亏损
                if 'avg_winning' in statistics and 'avg_losing' in statistics:
                    self.output(
                        f"Average winning: {statistics['avg_winning']:,.2f}")
                    self.output(
                        f"Average losing: {statistics['avg_losing']:,.2f}")

                # 显示总交易盈亏
                if 'total_trade_pnl' in statistics:
                    self.output(
                        f"Total trade PnL: {statistics['total_trade_pnl']:,.2f}")

            self.output("-" * 30)

        return statistics

    def show_chart(self) -> None:
        """
        Show chart of trading result.
        """
        # Check if result data is calculated
        if self.daily_df is None or self.daily_df.empty:
            self.calculate_result()

        try:
            import matplotlib.pyplot as plt
            import seaborn as sns

            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 6))

            # Plot balance curve
            plt.subplot(2, 1, 1)
            plt.title("Balance")
            plt.plot(self.daily_df["balance"])

            # Plot drawdown curve
            plt.subplot(2, 1, 2)
            plt.title("Drawdown")
            plt.fill_between(
                self.daily_df.index,
                self.daily_df["drawdown"].values,
                0,
                alpha=0.3,
                color="r"
            )

            plt.tight_layout()
            plt.show()
        except:
            self.output("Cannot show chart, matplotlib not installed")

    def update_daily_close(self, price: float) -> None:
        """
        Update daily close price.
        """
        # 确保datetime是正确的日期时间对象
        if hasattr(self.datetime, 'date'):
            d = self.datetime.date()
        else:
            # 如果是numpy.datetime64，转换为Python datetime
            try:
                from pandas import Timestamp
                d = Timestamp(self.datetime).date()
            except:
                # 如果转换失败，使用当前日期
                from datetime import datetime
                d = datetime.now().date()

        daily_result = self.daily_results.get(d, None)
        if daily_result:
            daily_result.close_price = price
        else:
            self.daily_results[d] = DailyResult(d, price)

    def register_event(self) -> None:
        """
        Register event handlers.
        """
        # 注册策略的事件处理函数
        self.event_engine.register(
            EventType.EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EventType.EVENT_BAR, self.process_bar_event)
        self.event_engine.register(
            EventType.EVENT_ORDER, self.process_order_event)
        self.event_engine.register(
            EventType.EVENT_TRADE, self.process_trade_event)
        self.event_engine.register(
            EventType.EVENT_STOP_ORDER, self.process_stop_order_event)
        self.event_engine.register(
            EventType.EVENT_INIT, self.process_init_event)
        self.event_engine.register(
            EventType.EVENT_START, self.process_start_event)
        self.event_engine.register(
            EventType.EVENT_STOP, self.process_stop_event)

    def process_tick_event(self, event: Event) -> None:
        """
        Process tick event.
        """
        tick = event.data
        self.strategy.on_tick(tick)

    def process_bar_event(self, event: Event) -> None:
        """
        Process bar event.
        """
        bar = event.data
        self.strategy.on_bar(bar)

    def process_order_event(self, event: Event) -> None:
        """
        Process order event.
        """
        order = event.data
        self.strategy.on_order(order)

    def process_trade_event(self, event: Event) -> None:
        """
        Process trade event.
        """
        trade = event.data
        self.strategy.on_trade(trade)

    def process_stop_order_event(self, event: Event) -> None:
        """
        Process stop order event.
        """
        stop_order = event.data
        self.strategy.on_stop_order(stop_order)

    def process_init_event(self, event: Event) -> None:
        """
        Process init event.
        """
        self.strategy.on_init()

    def process_start_event(self, event: Event) -> None:
        """
        Process start event.
        """
        self.strategy.on_start()

    def process_stop_event(self, event: Event) -> None:
        """
        Process stop event.
        """
        self.strategy.on_stop()

    def new_bar(self, bar: BarData) -> None:
        """
        Process new bar data with optimized performance using event-driven architecture.
        """
        # 使用直接赋值，避免额外的方法调用开销
        self.bar = bar
        self.datetime = bar.datetime

        # 使用预先计算的值，避免重复计算和查找
        self.current_bar_index = self._datetime_to_index_map.get(
            bar.datetime, -1)
        self.remaining_bars = bar.remaining_bars  # 直接访问属性，避免getattr开销

        # 使用内联条件判断，减少函数调用开销
        # 只有当有活跃订单时才执行订单处理
        if self.active_limit_orders:
            self.cross_limit_order()

        if self.active_stop_orders:
            self.cross_stop_order()

        # 通过事件引擎发送BAR事件
        self.event_engine.put(Event(EventType.EVENT_BAR, bar))

        # 更新每日收盘价 - 使用日期缓存优化
        d = bar.datetime.date()
        daily_result = self.daily_results.get(d)

        if daily_result is None:
            self.daily_results[d] = DailyResult(d, bar.close_price)
        else:
            daily_result.close_price = bar.close_price

    def new_tick(self, tick: TickData) -> None:
        """
        Process new tick data using event-driven architecture.
        """
        self.tick = tick
        self.datetime = tick.datetime

        self.cross_limit_order()
        self.cross_stop_order()

        # 通过事件引擎发送TICK事件
        self.event_engine.put(Event(EventType.EVENT_TICK, tick))

        self.update_daily_close(tick.last_price)

    def cross_limit_order(self) -> None:
        """
        Cross limit order with last bar/tick data - optimized for performance.
        """
        # 如果没有活跃订单，直接返回，避免不必要的计算
        if not self.active_limit_orders:
            return

        # 使用局部变量缓存价格，减少属性访问开销
        if self.mode == BacktestingMode.BAR:
            bar = self.bar  # 局部变量缓存
            long_cross_price = bar.low_price
            short_cross_price = bar.high_price
            long_best_price = bar.open_price
            short_best_price = bar.open_price
        else:
            tick = self.tick  # 局部变量缓存
            long_cross_price = tick.ask_price_1
            short_cross_price = tick.bid_price_1
            long_best_price = long_cross_price
            short_best_price = short_cross_price

        # 预先检查价格是否有效，避免在循环中重复检查
        if long_cross_price <= 0 or short_cross_price <= 0:
            return

        # 使用字典分组预先筛选可能成交的订单，减少循环次数
        # 这样可以避免创建临时列表
        submitting_orders = []
        long_cross_orders = []
        short_cross_orders = []

        # 预先获取活跃订单列表，避免在循环中修改字典
        active_orders = list(self.active_limit_orders.values())

        # 使用批处理方式处理订单，减少循环开销
        batch_size = 100  # 每批处理的订单数量
        for i in range(0, len(active_orders), batch_size):
            batch_orders = active_orders[i:i+batch_size]

            # 预先处理订单状态，减少循环中的条件判断
            for order in batch_orders:
                # 更新提交中订单的状态
                if order.status == Status.SUBMITTING:
                    submitting_orders.append(order)

                # 预先筛选可能成交的订单
                if order.direction == Direction.LONG and order.price >= long_cross_price:
                    long_cross_orders.append(order)
                elif order.direction == Direction.SHORT and order.price <= short_cross_price:
                    short_cross_orders.append(order)

        # 批量更新提交中订单的状态
        for order in submitting_orders:
            order.status = Status.NOTTRADED
            self.event_engine.put(Event(EventType.EVENT_ORDER, order))

        # 预先分配交易ID，避免在循环中递增
        next_trade_id = self.trade_count + 1
        trades_to_process = []
        orders_to_update = []

        # 批量处理多头订单
        for order in long_cross_orders:
            # 更新订单状态
            order.traded = order.volume
            order.status = Status.ALLTRADED
            orders_to_update.append(order)

            # 创建成交记录
            trade_price = min(order.price, long_best_price)

            trade = TradeData(
                symbol=order.symbol,
                exchange=order.exchange,
                orderid=order.orderid,
                tradeid=str(next_trade_id),
                direction=order.direction,
                offset=order.offset,
                price=trade_price,
                volume=order.volume,
                datetime=self.datetime,
                gateway_name=self.gateway_name,
            )
            next_trade_id += 1

            # 添加到待处理列表
            trades_to_process.append((order, trade))

        # 批量处理空头订单
        for order in short_cross_orders:
            # 更新订单状态
            order.traded = order.volume
            order.status = Status.ALLTRADED
            orders_to_update.append(order)

            # 创建成交记录
            trade_price = max(order.price, short_best_price)

            trade = TradeData(
                symbol=order.symbol,
                exchange=order.exchange,
                orderid=order.orderid,
                tradeid=str(next_trade_id),
                direction=order.direction,
                offset=order.offset,
                price=trade_price,
                volume=order.volume,
                datetime=self.datetime,
                gateway_name=self.gateway_name,
            )
            next_trade_id += 1

            # 添加到待处理列表
            trades_to_process.append((order, trade))

        # 批量更新交易计数
        self.trade_count = next_trade_id - 1

        # 批量处理所有交易
        for order, trade in trades_to_process:
            # 从活跃订单中移除
            self.active_limit_orders.pop(order.vt_orderid)

            # 保存成交记录并通过事件引擎通知策略
            self.trades[trade.vt_tradeid] = trade
            self.event_engine.put(Event(EventType.EVENT_ORDER, order))
            self.event_engine.put(Event(EventType.EVENT_TRADE, trade))

    def cross_stop_order(self) -> None:
        """
        Cross stop order with last bar/tick data - optimized for performance.
        """
        # 如果没有活跃止损订单，直接返回，避免不必要的计算
        if not self.active_stop_orders:
            return

        # 使用局部变量缓存价格，减少属性访问开销
        if self.mode == BacktestingMode.BAR:
            bar = self.bar  # 局部变量缓存
            long_cross_price = bar.high_price
            short_cross_price = bar.low_price
            long_best_price = bar.open_price
            short_best_price = bar.open_price
        else:
            tick = self.tick  # 局部变量缓存
            long_cross_price = tick.last_price
            short_cross_price = tick.last_price
            long_best_price = long_cross_price
            short_best_price = short_cross_price

        # 预先获取活跃止损订单列表，避免在循环中修改字典
        active_stop_orders = list(self.active_stop_orders.values())

        # 使用批处理方式处理订单，减少循环开销
        long_cross_orders = []
        short_cross_orders = []

        # 批量筛选可能触发的止损订单
        batch_size = 100  # 每批处理的订单数量
        for i in range(0, len(active_stop_orders), batch_size):
            batch_orders = active_stop_orders[i:i+batch_size]

            # 预先筛选可能触发的止损订单
            for stop_order in batch_orders:
                if stop_order.direction == Direction.LONG and stop_order.price <= long_cross_price:
                    long_cross_orders.append(stop_order)
                elif stop_order.direction == Direction.SHORT and stop_order.price >= short_cross_price:
                    short_cross_orders.append(stop_order)

        # 预先分配订单ID和交易ID，避免在循环中递增
        next_order_id = self.limit_order_count + 1
        next_trade_id = self.trade_count + 1

        # 预先创建所有需要处理的数据结构
        orders_to_create = []
        trades_to_create = []
        stop_orders_to_update = []

        # 批量处理多头止损订单
        for stop_order in long_cross_orders:
            # 创建订单数据
            order_id = str(next_order_id)
            next_order_id += 1

            order = OrderData(
                symbol=self.symbol,
                exchange=self.exchange,
                orderid=order_id,
                direction=stop_order.direction,
                offset=stop_order.offset,
                price=stop_order.price,
                volume=stop_order.volume,
                status=Status.ALLTRADED,
                gateway_name=self.gateway_name,
                datetime=self.datetime
            )

            # 创建成交数据
            trade_price = max(stop_order.price, long_best_price)
            trade_id = str(next_trade_id)
            next_trade_id += 1

            trade = TradeData(
                symbol=order.symbol,
                exchange=order.exchange,
                orderid=order.orderid,
                tradeid=trade_id,
                direction=order.direction,
                offset=order.offset,
                price=trade_price,
                volume=order.volume,
                datetime=self.datetime,
                gateway_name=self.gateway_name,
            )

            # 更新止损订单
            stop_order.vt_orderids.append(order.vt_orderid)
            stop_order.status = StopOrderStatus.TRIGGERED

            # 添加到待处理列表
            orders_to_create.append(order)
            trades_to_create.append(trade)
            stop_orders_to_update.append(stop_order)

        # 批量处理空头止损订单
        for stop_order in short_cross_orders:
            # 创建订单数据
            order_id = str(next_order_id)
            next_order_id += 1

            order = OrderData(
                symbol=self.symbol,
                exchange=self.exchange,
                orderid=order_id,
                direction=stop_order.direction,
                offset=stop_order.offset,
                price=stop_order.price,
                volume=stop_order.volume,
                status=Status.ALLTRADED,
                gateway_name=self.gateway_name,
                datetime=self.datetime
            )

            # 创建成交数据
            trade_price = min(stop_order.price, short_best_price)
            trade_id = str(next_trade_id)
            next_trade_id += 1

            trade = TradeData(
                symbol=order.symbol,
                exchange=order.exchange,
                orderid=order.orderid,
                tradeid=trade_id,
                direction=order.direction,
                offset=order.offset,
                price=trade_price,
                volume=order.volume,
                datetime=self.datetime,
                gateway_name=self.gateway_name,
            )

            # 更新止损订单
            stop_order.vt_orderids.append(order.vt_orderid)
            stop_order.status = StopOrderStatus.TRIGGERED

            # 添加到待处理列表
            orders_to_create.append(order)
            trades_to_create.append(trade)
            stop_orders_to_update.append(stop_order)

        # 批量更新计数器
        self.limit_order_count = next_order_id - 1
        self.trade_count = next_trade_id - 1

        # 批量处理所有订单和交易
        for i in range(len(orders_to_create)):
            order = orders_to_create[i]
            trade = trades_to_create[i]
            stop_order = stop_orders_to_update[i]

            # 保存订单和交易
            self.limit_orders[order.vt_orderid] = order
            self.trades[trade.vt_tradeid] = trade

            # 从活跃止损订单中移除
            self.active_stop_orders.pop(stop_order.stop_orderid)

            # 通过事件引擎通知策略
            self.event_engine.put(Event(EventType.EVENT_ORDER, order))
            self.event_engine.put(Event(EventType.EVENT_TRADE, trade))

    def send_order(
        self,
        strategy: StrategyTemplate,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float,
        stop: bool,
        lock: bool = False,
        net: bool = False
    ) -> List[str]:
        """
        Send a new order using event-driven architecture.
        """
        price = round_to(price, self.pricetick)
        if stop:
            vt_orderid = self.send_stop_order(
                direction, offset, price, volume)
        else:
            vt_orderid = self.send_limit_order(
                direction, offset, price, volume)
        return [vt_orderid]

    def send_stop_order(
        self,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float
    ) -> str:
        """
        Send a stop order.
        """
        self.stop_order_count += 1

        stop_order = StopOrder(
            vt_symbol=self.vt_symbol,
            direction=direction,
            offset=offset,
            price=price,
            volume=volume,
            stop_orderid=f"{STOPORDER_PREFIX}.{self.stop_order_count}",
            strategy_name=self.strategy.strategy_name,
            datetime=self.datetime,
            status=StopOrderStatus.WAITING,
            vt_orderids=[]
        )

        self.active_stop_orders[stop_order.stop_orderid] = stop_order
        self.stop_orders[stop_order.stop_orderid] = stop_order

        # 通过事件引擎发送停止单事件
        self.event_engine.put(Event(EventType.EVENT_STOP_ORDER, stop_order))

        return stop_order.stop_orderid

    def send_limit_order(
        self,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float
    ) -> str:
        """
        Send a limit order.
        """
        self.limit_order_count += 1

        order = OrderData(
            symbol=self.symbol,
            exchange=self.exchange,
            orderid=str(self.limit_order_count),
            direction=direction,
            offset=offset,
            price=price,
            volume=volume,
            status=Status.SUBMITTING,
            gateway_name=self.gateway_name,
            datetime=self.datetime,
            strategy_name=self.strategy.strategy_name if self.strategy else ""
        )

        self.active_limit_orders[order.vt_orderid] = order
        self.limit_orders[order.vt_orderid] = order

        # 通过事件引擎发送订单事件
        self.event_engine.put(Event(EventType.EVENT_ORDER, order))

        return order.vt_orderid

    def cancel_order(self, strategy: StrategyTemplate, vt_orderid: str) -> None:
        """
        Cancel an existing order.
        """
        if vt_orderid.startswith(STOPORDER_PREFIX):
            self.cancel_stop_order(strategy, vt_orderid)
        else:
            self.cancel_limit_order(strategy, vt_orderid)

    def cancel_limit_order(self, strategy: StrategyTemplate, vt_orderid: str) -> None:
        """
        Cancel a limit order.
        """
        if vt_orderid in self.active_limit_orders:
            order = self.active_limit_orders.pop(vt_orderid)

            order.status = Status.CANCELLED
            self.event_engine.put(Event(EventType.EVENT_ORDER, order))

    def cancel_stop_order(self, strategy: StrategyTemplate, vt_orderid: str) -> None:
        """
        Cancel a stop order.
        """
        if vt_orderid in self.active_stop_orders:
            stop_order = self.active_stop_orders.pop(vt_orderid)

            stop_order.status = StopOrderStatus.CANCELLED
            # 通过事件引擎发送停止单事件
            self.event_engine.put(
                Event(EventType.EVENT_STOP_ORDER, stop_order))

    def cancel_all(self, strategy: StrategyTemplate) -> None:
        """
        Cancel all orders.
        """
        vt_orderids = list(self.active_limit_orders.keys())
        for vt_orderid in vt_orderids:
            self.cancel_limit_order(strategy, vt_orderid)

        vt_orderids = list(self.active_stop_orders.keys())
        for vt_orderid in vt_orderids:
            self.cancel_stop_order(strategy, vt_orderid)

    def write_log(self, msg: str, strategy: StrategyTemplate = None) -> None:
        """
        Write log message.
        """
        msg = f"{self.datetime}\t{msg}"
        self.logs.append(msg)

        # 通过事件引擎发送日志事件
        log_event = Event(EventType.EVENT_LOG, msg)
        self.event_engine.put(log_event)

    def load_bar(
        self,
        vt_symbol: str,
        days: int,
        interval: Interval = None,
        callback: Callable = None
    ) -> List[BarData]:
        """
        Load historical bar data for initializing strategy.
        """
        self.days = days
        self.callback = callback

    def load_tick(self, vt_symbol: str, days: int, callback: Callable) -> List[TickData]:
        """
        Load historical tick data for initializing strategy.
        """
        self.days = days
        self.callback = callback

    def get_engine_type(self) -> EngineType:
        """
        Return engine type.
        """
        return self.engine_type

    def get_pricetick(self, strategy: StrategyTemplate) -> float:
        """
        Return contract pricetick data.
        """
        return self.pricetick

    def get_size(self, strategy: StrategyTemplate) -> int:
        """
        Return contract size data.
        """
        return self.size

    def get_max_positions(self) -> Optional[int]:
        """
        Get maximum number of positions allowed.

        Returns:
            Optional[int]: Maximum positions limit, None for unlimited.
        """
        return self.max_positions

    def output(self, msg) -> None:
        """
        Output message.
        """
        print(f"{datetime.now()}\t{msg}")
