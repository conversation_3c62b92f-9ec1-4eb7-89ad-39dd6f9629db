"""
日志引擎 - 用于将策略日志保存到文件
"""

import os
import datetime


class LogEngine:
    """
    日志引擎 - 用于将策略日志保存到文件
    """

    def __init__(self, log_file="vnpy_backtester/logs/strategy_log.log"):
        """
        初始化日志引擎

        参数:
        log_file: 日志文件路径，默认为vnpy_backtester/logs/strategy_log.log
        """
        self.log_file = log_file

        # 确保日志文件所在目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 清空日志文件
        with open(self.log_file, "w", encoding="utf-8") as f:
            f.write(f"===== 策略日志 {datetime.datetime.now()} =====\n")

    def write_log(self, msg):
        """
        写入日志

        参数:
        msg: 日志消息
        """
        # 只写入日志文件，不输出到控制台
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(f"{msg}\n")


# 创建全局日志引擎实例
log_engine = LogEngine()
