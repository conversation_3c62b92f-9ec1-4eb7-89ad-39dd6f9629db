"""
Data engine for backtesting
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Callable
import pandas as pd
import numpy as np
import os

from vnpy_backtester.utils.constant import Exchange, Interval
from vnpy_backtester.objects.object import BarData, TickData
from vnpy_backtester.utils.utility import extract_vt_symbol, load_bar_data_from_csv, generate_bar_from_ticks


class DataEngine:
    """
    Data engine for loading and processing data.
    """

    def __init__(self) -> None:
        """
        Initialize data engine.
        """
        self.bar_data: Dict[str, List[BarData]] = {}
        self.tick_data: Dict[str, List[TickData]] = {}

    def load_bar_data_from_csv(
        self,
        file_path: str,
        symbol: str,
        exchange: Exchange,
        interval: Interval,
        datetime_format: str = "%Y-%m-%d %H:%M:%S"
    ) -> List[BarData]:
        """
        Load bar data from csv file.
        """
        if not os.path.exists(file_path):
            print(f"File does not exist: {file_path}")
            return []

        bars = load_bar_data_from_csv(
            file_path,
            symbol,
            exchange,
            interval,
            datetime_format
        )

        vt_symbol = f"{symbol}.{exchange.value}"
        self.bar_data[vt_symbol] = bars

        print(f"Loaded {len(bars)} bars for {vt_symbol}")
        return bars

    def load_bar_data_from_dataframe(
        self,
        df: pd.DataFrame,
        symbol: str,
        exchange: Exchange,
        interval: Interval,
        datetime_key: str = "datetime",
        open_key: str = "open",
        high_key: str = "high",
        low_key: str = "low",
        close_key: str = "close",
        volume_key: str = "volume",
        turnover_key: str = "turnover",
        open_interest_key: str = "open_interest"
    ) -> List[BarData]:
        """
        Load bar data from dataframe.
        """
        bars = []

        for _, row in df.iterrows():
            bar = BarData(
                symbol=symbol,
                exchange=exchange,
                interval=interval,
                datetime=row[datetime_key],
                open_price=row[open_key],
                high_price=row[high_key],
                low_price=row[low_key],
                close_price=row[close_key],
                volume=row[volume_key],
                turnover=row.get(turnover_key, 0),
                open_interest=row.get(open_interest_key, 0),
                gateway_name="DF"
            )
            bars.append(bar)

        vt_symbol = f"{symbol}.{exchange.value}"
        self.bar_data[vt_symbol] = bars

        print(f"Loaded {len(bars)} bars for {vt_symbol}")
        return bars

    def load_tick_data_from_csv(
        self,
        file_path: str,
        symbol: str,
        exchange: Exchange,
        datetime_format: str = "%Y-%m-%d %H:%M:%S.%f"
    ) -> List[TickData]:
        """
        Load tick data from csv file.
        """
        if not os.path.exists(file_path):
            print(f"File does not exist: {file_path}")
            return []

        df = pd.read_csv(file_path)
        ticks = []

        for _, row in df.iterrows():
            tick = TickData(
                symbol=symbol,
                exchange=exchange,
                datetime=datetime.strptime(row["datetime"], datetime_format),
                name=symbol,
                volume=float(row["volume"]),
                turnover=float(row.get("turnover", 0)),
                open_interest=float(row.get("open_interest", 0)),
                last_price=float(row["last_price"]),
                last_volume=float(row.get("last_volume", 0)),
                limit_up=float(row.get("limit_up", 0)),
                limit_down=float(row.get("limit_down", 0)),
                open_price=float(row.get("open_price", 0)),
                high_price=float(row.get("high_price", 0)),
                low_price=float(row.get("low_price", 0)),
                pre_close=float(row.get("pre_close", 0)),
                bid_price_1=float(row.get("bid_price_1", 0)),
                bid_price_2=float(row.get("bid_price_2", 0)),
                bid_price_3=float(row.get("bid_price_3", 0)),
                bid_price_4=float(row.get("bid_price_4", 0)),
                bid_price_5=float(row.get("bid_price_5", 0)),
                ask_price_1=float(row.get("ask_price_1", 0)),
                ask_price_2=float(row.get("ask_price_2", 0)),
                ask_price_3=float(row.get("ask_price_3", 0)),
                ask_price_4=float(row.get("ask_price_4", 0)),
                ask_price_5=float(row.get("ask_price_5", 0)),
                bid_volume_1=float(row.get("bid_volume_1", 0)),
                bid_volume_2=float(row.get("bid_volume_2", 0)),
                bid_volume_3=float(row.get("bid_volume_3", 0)),
                bid_volume_4=float(row.get("bid_volume_4", 0)),
                bid_volume_5=float(row.get("bid_volume_5", 0)),
                ask_volume_1=float(row.get("ask_volume_1", 0)),
                ask_volume_2=float(row.get("ask_volume_2", 0)),
                ask_volume_3=float(row.get("ask_volume_3", 0)),
                ask_volume_4=float(row.get("ask_volume_4", 0)),
                ask_volume_5=float(row.get("ask_volume_5", 0)),
                gateway_name="CSV"
            )
            ticks.append(tick)

        vt_symbol = f"{symbol}.{exchange.value}"
        self.tick_data[vt_symbol] = ticks

        print(f"Loaded {len(ticks)} ticks for {vt_symbol}")
        return ticks

    def get_bar_data(
        self,
        vt_symbol: str,
        start: datetime = None,
        end: datetime = None
    ) -> List[BarData]:
        """
        Get bar data by vt_symbol.
        """
        bars = self.bar_data.get(vt_symbol, [])

        if not bars:
            return []

        if start:
            bars = [bar for bar in bars if bar.datetime >= start]

        if end:
            bars = [bar for bar in bars if bar.datetime <= end]

        return bars

    def get_tick_data(
        self,
        vt_symbol: str,
        start: datetime = None,
        end: datetime = None
    ) -> List[TickData]:
        """
        Get tick data by vt_symbol.
        """
        ticks = self.tick_data.get(vt_symbol, [])

        if not ticks:
            return []

        if start:
            ticks = [tick for tick in ticks if tick.datetime >= start]

        if end:
            ticks = [tick for tick in ticks if tick.datetime <= end]

        return ticks

    def convert_ticks_to_bars(
        self,
        vt_symbol: str,
        interval: Interval
    ) -> List[BarData]:
        """
        Convert tick data to bar data.
        """
        ticks = self.tick_data.get(vt_symbol, [])
        if not ticks:
            return []

        bars = generate_bar_from_ticks(ticks, interval)

        self.bar_data[vt_symbol] = bars

        return bars
