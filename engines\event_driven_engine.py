"""
Event-driven backtesting engine base class.
This module provides the foundation for event-driven backtesting architecture.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import pandas as pd

from vnpy_backtester.utils.event import Event, EventType, EventEngine
from vnpy_backtester.utils.pnl_manager import PnLManager
from vnpy_backtester.objects.object import BarData, TradeData, OrderData
from vnpy_backtester.utils.constant import Direction, Offset, Status
from vnpy_backtester.utils.base import DailyResult


class EventDrivenEngine(ABC):
    """
    事件驱动回测引擎基类
    
    提供统一的事件驱动架构，所有引擎都应该继承此类
    """
    
    def __init__(self, event_engine: EventEngine = None):
        """
        初始化事件驱动引擎
        
        参数:
        event_engine: 事件引擎，如果为None则创建新的
        """
        self.event_engine = event_engine or EventEngine()
        self.pnl_manager = PnLManager(self.event_engine)
        
        # 引擎状态
        self.active = False
        self.initialized = False
        
        # 数据存储
        self.current_datetime: Optional[datetime] = None
        self.current_bar: Optional[BarData] = None
        
        # 注册事件处理器
        self._register_event_handlers()
        
    def _register_event_handlers(self):
        """注册事件处理器"""
        # 基础事件处理器
        self.event_engine.register(EventType.EVENT_INIT, self._on_init_event)
        self.event_engine.register(EventType.EVENT_START, self._on_start_event)
        self.event_engine.register(EventType.EVENT_STOP, self._on_stop_event)
        self.event_engine.register(EventType.EVENT_BAR, self._on_bar_event)
        self.event_engine.register(EventType.EVENT_TRADE, self._on_trade_event)
        self.event_engine.register(EventType.EVENT_ORDER, self._on_order_event)
        
        # 回测专用事件处理器
        self.event_engine.register(EventType.EVENT_BACKTESTING_START, self._on_backtesting_start_event)
        self.event_engine.register(EventType.EVENT_BACKTESTING_STOP, self._on_backtesting_stop_event)
        self.event_engine.register(EventType.EVENT_DAILY_RESULT, self._on_daily_result_event)
        self.event_engine.register(EventType.EVENT_PNL_UPDATE, self._on_pnl_update_event)
        
    def start_engine(self):
        """启动引擎"""
        if not self.event_engine._active:
            self.event_engine.start()
        self.active = True
        
        # 发布启动事件
        event = Event(EventType.EVENT_START, source=self.__class__.__name__)
        self.event_engine.put(event)
        
    def stop_engine(self):
        """停止引擎"""
        self.active = False
        
        # 发布停止事件
        event = Event(EventType.EVENT_STOP, source=self.__class__.__name__)
        self.event_engine.put(event)
        
        if self.event_engine._active:
            self.event_engine.stop()
            
    def initialize(self):
        """初始化引擎"""
        if self.initialized:
            return
            
        self.initialized = True
        
        # 发布初始化事件
        event = Event(EventType.EVENT_INIT, source=self.__class__.__name__)
        self.event_engine.put(event)
        
    def put_event(self, event: Event):
        """发布事件"""
        if not event.source:
            event.source = self.__class__.__name__
        self.event_engine.put(event)
        
    def process_bar(self, bar: BarData):
        """处理K线数据"""
        self.current_bar = bar
        self.current_datetime = bar.datetime
        
        # 发布K线事件
        event = Event(EventType.EVENT_BAR, data=bar, source=self.__class__.__name__)
        self.event_engine.put(event)
        
    def process_trade(self, trade: TradeData):
        """处理交易数据"""
        # 使用PnL管理器计算盈亏
        pnl_result = self.pnl_manager.process_trade(trade)
        
        # 发布交易事件
        event = Event(
            EventType.EVENT_TRADE, 
            data={"trade": trade, "pnl_result": pnl_result}, 
            source=self.__class__.__name__
        )
        self.event_engine.put(event)
        
    def process_order(self, order: OrderData):
        """处理订单数据"""
        # 发布订单事件
        event = Event(EventType.EVENT_ORDER, data=order, source=self.__class__.__name__)
        self.event_engine.put(event)
        
    def process_daily_result(self, daily_result: DailyResult):
        """处理日结果数据"""
        # 发布日结果事件
        event = Event(EventType.EVENT_DAILY_RESULT, data=daily_result, source=self.__class__.__name__)
        self.event_engine.put(event)
        
    # 事件处理器方法
    def _on_init_event(self, event: Event):
        """处理初始化事件"""
        self.on_init()
        
    def _on_start_event(self, event: Event):
        """处理启动事件"""
        self.on_start()
        
    def _on_stop_event(self, event: Event):
        """处理停止事件"""
        self.on_stop()
        
    def _on_bar_event(self, event: Event):
        """处理K线事件"""
        bar = event.data
        self.on_bar(bar)
        
    def _on_trade_event(self, event: Event):
        """处理交易事件"""
        trade_data = event.data
        trade = trade_data.get("trade")
        pnl_result = trade_data.get("pnl_result")
        self.on_trade(trade, pnl_result)
        
    def _on_order_event(self, event: Event):
        """处理订单事件"""
        order = event.data
        self.on_order(order)
        
    def _on_backtesting_start_event(self, event: Event):
        """处理回测开始事件"""
        self.on_backtesting_start()
        
    def _on_backtesting_stop_event(self, event: Event):
        """处理回测停止事件"""
        self.on_backtesting_stop()
        
    def _on_daily_result_event(self, event: Event):
        """处理日结果事件"""
        daily_result = event.data
        self.on_daily_result(daily_result)
        
    def _on_pnl_update_event(self, event: Event):
        """处理盈亏更新事件"""
        pnl_data = event.data
        self.on_pnl_update(pnl_data)
        
    # 抽象方法 - 子类必须实现
    @abstractmethod
    def on_init(self):
        """初始化回调"""
        pass
        
    @abstractmethod
    def on_start(self):
        """启动回调"""
        pass
        
    @abstractmethod
    def on_stop(self):
        """停止回调"""
        pass
        
    # 可选实现的方法
    def on_bar(self, bar: BarData):
        """K线回调"""
        pass
        
    def on_trade(self, trade: TradeData, pnl_result: Any):
        """交易回调"""
        pass
        
    def on_order(self, order: OrderData):
        """订单回调"""
        pass
        
    def on_backtesting_start(self):
        """回测开始回调"""
        pass
        
    def on_backtesting_stop(self):
        """回测停止回调"""
        pass
        
    def on_daily_result(self, daily_result: DailyResult):
        """日结果回调"""
        pass
        
    def on_pnl_update(self, pnl_data: Dict[str, Any]):
        """盈亏更新回调"""
        pass
        
    def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        stats = {
            "engine_name": self.__class__.__name__,
            "active": self.active,
            "initialized": self.initialized,
            "current_datetime": self.current_datetime,
            "event_engine_stats": self.event_engine.get_statistics(),
            "pnl_stats": self.pnl_manager.get_statistics()
        }
        return stats
        
    def set_debug_mode(self, debug: bool):
        """设置调试模式"""
        self.event_engine.set_debug_mode(debug)
