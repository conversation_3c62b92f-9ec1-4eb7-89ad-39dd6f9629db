"""
图表引擎 - 使用Plotly绘制回测结果图表
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime
import traceback


class PlotlyChartEngine:
    """
    Plotly图表引擎 - 用于绘制回测结果图表
    """

    def __init__(self):
        """初始化图表引擎"""
        self.fig = None

    def create_chart(self, engine, title="回测结果", save_path=None, show=True, initial_capital=None):
        """
        创建回测结果图表

        参数:
        engine: BacktestingEngine实例
        title: 图表标题
        save_path: 保存路径，如果为None则不保存
        show: 是否显示图表
        initial_capital: 初始资金，如果为None则尝试从engine获取

        返回:
        fig: Plotly图表对象
        """
        # 获取初始资金
        if initial_capital is None:
            if hasattr(engine, 'capital'):
                initial_capital = engine.capital
            elif hasattr(engine, 'stats') and 'capital' in engine.stats:
                initial_capital = engine.stats['capital']
            else:
                # 从统计结果中获取
                stats = engine.calculate_statistics()
                initial_capital = stats.get('capital', 100000)
        try:
            # 获取回测结果数据
            daily_results = engine.daily_results

            # 如果daily_results是字典
            if isinstance(daily_results, dict):
                # 获取日期列表（键）并排序
                dates = list(daily_results.keys())
                dates.sort()  # 确保日期是有序的

                # 创建资金曲线数据
                balance = []
                returns = []
                drawdown = []

                # 创建累积的净盈亏列表
                cumulative_pnl = []
                daily_pnl = []

                # 提取数据
                for date in dates:
                    result = daily_results[date]

                    # 检查result的类型和结构
                    if isinstance(result, dict):
                        # 如果是字典，直接获取值
                        net_pnl = result.get('net_pnl', 0)
                        daily_pnl.append(net_pnl)

                        # 计算累积盈亏
                        if cumulative_pnl:
                            cumulative_pnl.append(cumulative_pnl[-1] + net_pnl)
                        else:
                            cumulative_pnl.append(net_pnl)
                    else:
                        # 如果是对象，尝试获取属性
                        # 首先检查是否有__dict__属性
                        if hasattr(result, '__dict__'):
                            result_dict = result.__dict__
                            # 不再打印调试信息

                            # 获取当日净盈亏
                            if 'net_pnl' in result_dict:
                                net_pnl = result_dict['net_pnl']
                            else:
                                net_pnl = 0

                            daily_pnl.append(net_pnl)

                            # 计算累积盈亏
                            if cumulative_pnl:
                                cumulative_pnl.append(
                                    cumulative_pnl[-1] + net_pnl)
                            else:
                                cumulative_pnl.append(net_pnl)
                        else:
                            # 如果没有__dict__属性，尝试直接访问属性
                            if hasattr(result, 'net_pnl'):
                                net_pnl = result.net_pnl
                            else:
                                net_pnl = 0

                            daily_pnl.append(net_pnl)

                            # 计算累积盈亏
                            if cumulative_pnl:
                                cumulative_pnl.append(
                                    cumulative_pnl[-1] + net_pnl)
                            else:
                                cumulative_pnl.append(net_pnl)

                # 计算每日资金余额
                balance = [initial_capital + pnl for pnl in cumulative_pnl]

                # 计算每日收益率
                returns = []
                for i in range(len(balance)):
                    if i == 0 or balance[i-1] == 0:
                        returns.append(0)
                    else:
                        daily_return = (
                            balance[i] - balance[i-1]) / balance[i-1] * 100
                        returns.append(daily_return)

                # 计算回撤
                drawdown = []
                max_balance = initial_capital
                for bal in balance:
                    max_balance = max(max_balance, bal)
                    drawdown_val = (max_balance - bal) / \
                        max_balance * 100 if max_balance > 0 else 0
                    drawdown.append(drawdown_val)

                # 确保数据有足够的点
                if len(balance) <= 1:
                    # 如果数据点太少，无法绘制有意义的图表
                    return None

                # 将日期转换为字符串格式
                date_strings = [date.strftime(
                    '%Y-%m-%d') if hasattr(date, 'strftime') else str(date) for date in dates]

                # 创建Plotly图表
                self.fig = make_subplots(
                    rows=3,
                    cols=1,
                    shared_xaxes=True,
                    vertical_spacing=0.05,
                    subplot_titles=("资金曲线", "收益率", "回撤"),
                    row_heights=[0.5, 0.25, 0.25]
                )

                # 添加资金曲线
                self.fig.add_trace(
                    go.Scatter(
                        x=date_strings,
                        y=balance,
                        mode="lines",
                        name="资金曲线",
                        line=dict(color="blue", width=2)
                    ),
                    row=1, col=1
                )

                # 添加收益率
                self.fig.add_trace(
                    go.Bar(
                        x=date_strings,
                        y=returns,
                        name="日收益率",
                        marker_color=["green" if r >=
                                      0 else "red" for r in returns]
                    ),
                    row=2, col=1
                )

                # 添加回撤
                self.fig.add_trace(
                    go.Scatter(
                        x=date_strings,
                        y=drawdown,
                        mode="lines",
                        name="回撤",
                        line=dict(color="red", width=2)
                    ),
                    row=3, col=1
                )

                # 更新布局 - 不指定固定大小，让图表自动适应浏览器窗口
                self.fig.update_layout(
                    title=title,
                    autosize=True,  # 自动调整大小
                    showlegend=False,
                    template="plotly_white"
                )

                # 显示图表
                if show:
                    self.fig.show(renderer="browser")  # 使用浏览器渲染器

                # 保存图表
                if save_path:
                    # 确保目录存在
                    import os
                    save_dir = os.path.dirname(save_path)
                    if save_dir and not os.path.exists(save_dir):
                        os.makedirs(save_dir, exist_ok=True)

                    # 添加响应式布局配置
                    self.fig.write_html(
                        save_path,
                        config={
                            'responsive': True,  # 响应式布局
                            'scrollZoom': True,  # 允许滚轮缩放
                            'displayModeBar': True,  # 显示模式栏
                            'displaylogo': False,  # 不显示Plotly logo
                        }
                    )
                    # 不在这里打印保存信息，避免重复

                return self.fig
            else:
                print(f"daily_results不是字典类型，无法处理")
                return None

        except Exception as e:
            print(f"创建图表时出错: {e}")
            traceback.print_exc()
            return None

    def save_chart(self, path):
        """
        保存图表

        参数:
        path: 保存路径
        """
        if self.fig:
            try:
                # 确保目录存在
                import os
                save_dir = os.path.dirname(path)
                if save_dir and not os.path.exists(save_dir):
                    os.makedirs(save_dir, exist_ok=True)

                # 添加响应式布局配置
                self.fig.write_html(
                    path,
                    config={
                        'responsive': True,  # 响应式布局
                        'scrollZoom': True,  # 允许滚轮缩放
                        'displayModeBar': True,  # 显示模式栏
                        'displaylogo': False,  # 不显示Plotly logo
                    }
                )
                print(f"图表已保存为 {path}")
                return True
            except Exception as e:
                print(f"保存图表时出错: {e}")
                traceback.print_exc()
                return False
        else:
            print("没有图表可保存")
            return False

    def show_chart(self):
        """显示图表"""
        if self.fig:
            self.fig.show(renderer="browser")  # 使用浏览器渲染器
        else:
            print("没有图表可显示")
