"""
Enhanced Chart Engine with unified PnL calculation.
This module provides event-driven chart generation with consistent PnL data.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from vnpy_backtester.engines.event_driven_engine import EventDrivenEngine
from vnpy_backtester.utils.event import Event, EventType
from vnpy_backtester.utils.pnl_manager import PnLManager
from vnpy_backtester.objects.object import TradeData
from vnpy_backtester.utils.base import DailyResult


class ChartEngine(EventDrivenEngine):
    """
    事件驱动的图表引擎
    使用统一的PnL管理器确保数据一致性
    """
    
    def __init__(self, event_engine=None):
        """初始化图表引擎"""
        super().__init__(event_engine)
        
        # 图表数据
        self.balance_data: List[float] = []
        self.datetime_data: List[datetime] = []
        self.pnl_data: List[float] = []
        self.drawdown_data: List[float] = []
        self.trade_data: List[Dict[str, Any]] = []
        
        # 初始资金
        self.initial_capital: float = 100000.0
        self.current_balance: float = 0.0
        self.max_balance: float = 0.0
        
        # 图表配置
        self.chart_title: str = "回测结果"
        self.save_path: Optional[str] = None
        self.show_chart: bool = True
        
    def on_init(self):
        """初始化回调"""
        self.write_log("ChartEngine initialized")
        
    def on_start(self):
        """启动回调"""
        self.write_log("ChartEngine started")
        self.current_balance = self.initial_capital
        self.max_balance = self.initial_capital
        
    def on_stop(self):
        """停止回调"""
        self.write_log("ChartEngine stopped")
        
    def on_trade(self, trade: TradeData, pnl_result: Any):
        """交易回调 - 记录交易数据"""
        if pnl_result:
            trade_info = {
                "datetime": trade.datetime,
                "symbol": trade.vt_symbol,
                "direction": trade.direction.value,
                "offset": trade.offset.value,
                "price": trade.price,
                "volume": trade.volume,
                "pnl": pnl_result.trade_pnl,
                "commission": pnl_result.commission,
                "slippage": pnl_result.slippage,
                "net_pnl": pnl_result.net_pnl,
                "return_rate": pnl_result.return_rate
            }
            self.trade_data.append(trade_info)
            
    def on_pnl_update(self, pnl_data: Dict[str, Any]):
        """盈亏更新回调 - 更新资金曲线"""
        total_net_pnl = pnl_data.get("total_net_pnl", 0.0)
        trade = pnl_data.get("trade")
        
        if trade:
            # 更新当前余额
            self.current_balance = self.initial_capital + total_net_pnl
            
            # 更新最大余额
            self.max_balance = max(self.max_balance, self.current_balance)
            
            # 计算回撤
            drawdown = (self.max_balance - self.current_balance) / self.max_balance * 100 if self.max_balance > 0 else 0.0
            
            # 记录数据
            self.datetime_data.append(trade.datetime)
            self.balance_data.append(self.current_balance)
            self.pnl_data.append(total_net_pnl)
            self.drawdown_data.append(drawdown)
            
    def on_daily_result(self, daily_result: DailyResult):
        """日结果回调"""
        # 可以在这里处理日结果数据
        pass
        
    def set_initial_capital(self, capital: float):
        """设置初始资金"""
        self.initial_capital = capital
        self.current_balance = capital
        self.max_balance = capital
        
    def set_chart_config(self, title: str = None, save_path: str = None, show: bool = True):
        """设置图表配置"""
        if title:
            self.chart_title = title
        if save_path:
            self.save_path = save_path
        self.show_chart = show
        
    def create_chart(self) -> go.Figure:
        """创建图表"""
        if not self.datetime_data:
            self.write_log("No data available for chart creation")
            return None
            
        # 创建子图
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('资金曲线', '累计盈亏', '回撤'),
            vertical_spacing=0.08,
            row_heights=[0.5, 0.25, 0.25]
        )
        
        # 资金曲线
        fig.add_trace(
            go.Scatter(
                x=self.datetime_data,
                y=self.balance_data,
                mode='lines',
                name='资金曲线',
                line=dict(color='blue', width=2),
                hovertemplate='时间: %{x}<br>余额: %{y:,.2f}<extra></extra>'
            ),
            row=1, col=1
        )
        
        # 添加初始资金线
        fig.add_hline(
            y=self.initial_capital,
            line_dash="dash",
            line_color="gray",
            annotation_text=f"初始资金: {self.initial_capital:,.0f}",
            row=1, col=1
        )
        
        # 累计盈亏
        colors = ['red' if pnl < 0 else 'green' for pnl in self.pnl_data]
        fig.add_trace(
            go.Scatter(
                x=self.datetime_data,
                y=self.pnl_data,
                mode='lines',
                name='累计盈亏',
                line=dict(color='green', width=2),
                fill='tonexty',
                hovertemplate='时间: %{x}<br>累计盈亏: %{y:,.2f}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # 回撤
        fig.add_trace(
            go.Scatter(
                x=self.datetime_data,
                y=self.drawdown_data,
                mode='lines',
                name='回撤',
                line=dict(color='red', width=2),
                fill='tozeroy',
                hovertemplate='时间: %{x}<br>回撤: %{y:.2f}%<extra></extra>'
            ),
            row=3, col=1
        )
        
        # 更新布局
        fig.update_layout(
            title=dict(
                text=self.chart_title,
                x=0.5,
                font=dict(size=16)
            ),
            height=800,
            showlegend=True,
            hovermode='x unified'
        )
        
        # 更新x轴
        fig.update_xaxes(title_text="时间", row=3, col=1)
        
        # 更新y轴
        fig.update_yaxes(title_text="资金", row=1, col=1)
        fig.update_yaxes(title_text="盈亏", row=2, col=1)
        fig.update_yaxes(title_text="回撤(%)", row=3, col=1)
        
        return fig
        
    def show_and_save_chart(self):
        """显示和保存图表"""
        fig = self.create_chart()
        if not fig:
            return
            
        # 保存图表
        if self.save_path:
            try:
                fig.write_html(self.save_path)
                self.write_log(f"Chart saved to: {self.save_path}")
            except Exception as e:
                self.write_log(f"Failed to save chart: {e}")
                
        # 显示图表
        if self.show_chart:
            try:
                fig.show()
                self.write_log("Chart displayed")
            except Exception as e:
                self.write_log(f"Failed to show chart: {e}")
                
    def get_chart_statistics(self) -> Dict[str, Any]:
        """获取图表统计信息"""
        if not self.balance_data:
            return {}
            
        final_balance = self.balance_data[-1] if self.balance_data else self.initial_capital
        total_return = (final_balance - self.initial_capital) / self.initial_capital * 100
        max_drawdown = max(self.drawdown_data) if self.drawdown_data else 0.0
        
        return {
            "initial_capital": self.initial_capital,
            "final_balance": final_balance,
            "total_return": total_return,
            "max_drawdown": max_drawdown,
            "total_trades": len(self.trade_data),
            "data_points": len(self.datetime_data)
        }
        
    def reset_data(self):
        """重置所有数据"""
        self.balance_data.clear()
        self.datetime_data.clear()
        self.pnl_data.clear()
        self.drawdown_data.clear()
        self.trade_data.clear()
        self.current_balance = self.initial_capital
        self.max_balance = self.initial_capital
        
    def write_log(self, msg: str):
        """写日志"""
        print(f"[ChartEngine] {msg}")
