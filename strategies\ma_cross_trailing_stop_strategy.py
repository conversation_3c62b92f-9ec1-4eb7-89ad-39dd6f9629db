"""
MA交叉策略 - 带移动止损功能
当MA10上穿MA20时反手做多，下穿时反手做空
不使用固定持仓K线数量，而是在出现反向信号时平仓并反向开仓
增加移动止损功能：
1. 每次开仓后，设置3%(参数可调)的止盈止损线
2. 当跌破止损线后，立即止损
3. 涨破止盈线后，不止盈，记录K线最高价(每根K线都重新记录)和当前止盈价
4. 当利润回撤到最高价和止盈线之间60%时，进行止盈
"""

from typing import List
import numpy as np
from datetime import datetime

from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.log_engine import log_engine
from vnpy_backtester.utils.array_manager import ArrayManager


class Position:
    """
    持仓类 - 用于记录每个持仓的信息
    """

    def __init__(self, direction: str, price: float, volume: float, entry_time: datetime):
        """
        初始化持仓

        参数:
        direction: 方向，"long"或"short"
        price: 开仓价格
        volume: 开仓数量
        entry_time: 开仓时间
        """
        self.direction = direction  # "long"或"short"
        self.price = price  # 开仓价格
        self.volume = volume  # 开仓数量
        self.entry_time = entry_time  # 开仓时间

        # 移动止损相关参数
        self.highest_price = price  # 持仓期间的最高价（多头）或最低价（空头）
        self.stop_loss_price = 0.0  # 止损价格
        self.take_profit_price = 0.0  # 止盈价格


class MACrossTrailingStopStrategy(StrategyTemplate):
    """
    MA交叉策略 - 带移动止损功能

    策略逻辑：
    1. 当短期均线(MA10)上穿长期均线(MA20)时反手做多
    2. 当短期均线(MA10)下穿长期均线(MA20)时反手做空
    3. 不使用固定持仓K线数量，而是在出现反向信号时平仓并反向开仓
    4. 每次开仓后，设置止盈止损线
    5. 当跌破止损线后，立即止损
    6. 涨破止盈线后，不止盈，记录K线最高价和当前止盈价
    7. 当利润回撤到最高价和止盈线之间指定比例时，进行止盈
    """

    # 策略参数
    fast_window = 10  # 快速均线窗口
    slow_window = 20  # 慢速均线窗口
    position_size = 10  # 每次开仓的数量、金额或资金比例
    size = 1  # 合约乘数
    commission_rate = 0.0003  # 默认手续费率，将从引擎获取
    slippage_rate = 0.001  # 默认滑点率，将从引擎获取
    # 下单方式，可选"quantity"(按币数量)、"amount"(按金额)或"ratio"(按资金比例)
    order_type = "quantity"

    # 移动止损参数
    stop_loss_pct = 3.0  # 止损百分比，默认为3%
    profit_take_ratio = 0.6  # 回撤比例，当利润回撤到60%时止盈

    # 策略变量
    fast_ma = 0.0
    slow_ma = 0.0
    positions = []  # 持仓列表，每个元素是一个Position对象

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.positions = []  # 初始化持仓列表
        self.current_pos_direction = ""  # 当前持仓方向，"long"或"short"或空字符串
        self.debug_mode = False  # 调试模式，默认关闭

        # 从设置中获取参数
        if "fast_window" in self.setting:
            self.fast_window = self.setting["fast_window"]
        if "slow_window" in self.setting:
            self.slow_window = self.setting["slow_window"]
        if "position_size" in self.setting:
            self.position_size = self.setting["position_size"]
        if "debug_mode" in self.setting:
            self.debug_mode = self.setting["debug_mode"]
        if "order_type" in self.setting:
            self.order_type = self.setting["order_type"]
            self.write_log(f"设置下单方式: {self.order_type}")

        # 获取移动止损参数
        if "stop_loss_pct" in self.setting:
            self.stop_loss_pct = self.setting["stop_loss_pct"]
        if "profit_take_ratio" in self.setting:
            self.profit_take_ratio = self.setting["profit_take_ratio"]

        self.write_log(
            f"设置止损百分比: {self.stop_loss_pct}%, 回撤止盈比例: {self.profit_take_ratio * 100}%")

        # 记录下单方式和仓位大小
        if self.order_type == "quantity":
            self.write_log(f"设置每次开仓数量: {self.position_size}")
        elif self.order_type == "amount":
            self.write_log(f"设置每次开仓金额: {self.position_size}")
        elif self.order_type == "ratio":
            ratio_percent = self.position_size * 100
            # 获取初始资金
            initial_capital = 50000.0  # 默认值
            if hasattr(self, "engine") and hasattr(self.engine, "capital"):
                initial_capital = self.engine.capital
            self.write_log(
                f"设置每次开仓资金比例: {ratio_percent:.2f}%, 初始资金: {initial_capital:.2f}, 每次开仓金额将根据当前账户总价值动态计算")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

        # 创建K线管理器，大小为慢速均线窗口的1.5倍，确保有足够的数据计算均线和判断交叉
        # 但不要过大，避免数据不足时计算短期均线出现问题
        self.am = ArrayManager(size=max(int(self.slow_window * 1.5), 100))

        # 记录上一根K线的均线值，用于判断交叉
        self.last_fast_ma = 0.0
        self.last_slow_ma = 0.0

    def on_init(self):
        """
        策略初始化
        """
        self.write_log("MA交叉策略(带移动止损)初始化")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

    def on_start(self):
        """
        策略启动
        """
        self.write_log("MA交叉策略(带移动止损)启动")

    def on_stop(self):
        """
        策略停止
        """
        self.write_log("MA交叉策略(带移动止损)停止")

    def write_log(self, msg, level="INFO"):
        """
        写入日志

        参数:
        msg: 日志消息
        level: 日志级别，可以是"DEBUG", "INFO", "WARNING", "ERROR"
        """
        # 只有在非DEBUG级别或者DEBUG模式开启时才记录日志
        if level != "DEBUG" or getattr(self, "debug_mode", False):
            # 使用日志引擎写入日志
            log_engine.write_log(f"{self.strategy_name} - {msg}")

            # 同时调用父类的write_log方法
            super().write_log(msg)

    def get_current_equity(self):
        """
        获取当前账户总价值（包括未实现盈亏）

        返回:
        float: 当前账户总价值
        """
        # 从引擎获取初始资金
        initial_capital = 50000.0  # 默认值
        if hasattr(self, "engine") and hasattr(self.engine, "capital"):
            initial_capital = self.engine.capital

        # 计算未实现盈亏（包括交易成本）
        unrealized_pnl = 0.0
        total_commission = 0.0
        total_slippage = 0.0

        for position in self.positions:
            # 计算持仓盈亏
            if position.direction == "long":
                # 多头持仓的未实现盈亏
                unrealized_pnl += (self.bar.close_price -
                                   position.price) * position.volume
            else:  # direction == "short"
                # 空头持仓的未实现盈亏
                unrealized_pnl += (position.price -
                                   self.bar.close_price) * position.volume

            # 计算开仓时的手续费
            open_commission = position.volume * self.size * \
                position.price * self.commission_rate
            # 计算平仓时的手续费（假设按当前价格平仓）
            close_commission = position.volume * self.size * \
                self.bar.close_price * self.commission_rate
            # 计算滑点
            open_slippage = position.volume * self.size * self.slippage_rate
            close_slippage = position.volume * self.size * self.slippage_rate

            # 累加交易成本
            total_commission += open_commission + close_commission
            total_slippage += open_slippage + close_slippage

        # 如果引擎有已实现盈亏的记录，也加入计算
        realized_pnl = 0.0
        if hasattr(self, "engine") and hasattr(self.engine, "net_pnl"):
            realized_pnl = self.engine.net_pnl

        # 计算当前账户总价值 = 初始资金 + 已实现盈亏 + 未实现盈亏 - 交易成本
        current_equity = initial_capital + realized_pnl + \
            unrealized_pnl - total_commission - total_slippage

        # 确保账户总价值不小于1.0，避免除以0的情况
        current_equity = max(1.0, current_equity)

        return current_equity

    def get_available_capital(self, force_recalculate=False):
        """
        获取可用资金

        参数:
        force_recalculate: 是否强制重新计算，不使用缓存

        返回:
        float: 可用资金
        """
        # 缓存计算结果，避免频繁重复计算
        if not force_recalculate and hasattr(self, '_last_bar_datetime') and self._last_bar_datetime == self.bar.datetime:
            return self._cached_available_capital

        # 获取当前账户总价值（包括未实现盈亏）
        current_equity = self.get_current_equity()

        # 计算已使用资金
        used_capital = 0.0
        for position in self.positions:
            used_capital += position.price * position.volume

        # 更新已使用资金缓存
        self._used_capital = used_capital

        # 计算可用资金 = 当前账户总价值 - 已使用资金
        available_capital = current_equity - used_capital

        # 确保可用资金不小于0
        available_capital = max(0.0, available_capital)

        # 缓存计算结果
        self._last_bar_datetime = self.bar.datetime
        self._cached_available_capital = available_capital

        return available_capital

    def on_bar(self, bar: BarData):
        """
        K线更新回调
        """
        # 保存当前K线对象，供其他方法使用
        self.bar = bar

        # 更新K线管理器
        self.am.update_bar(bar.open_price, bar.high_price,
                           bar.low_price, bar.close_price, bar.volume)

        # 如果数据不足，则不进行交易
        if not self.am.inited:
            self.write_log(
                f"数据不足，无法计算均线：{bar.datetime}, 数据点数量：{self.am.count}/{self.slow_window}", level="DEBUG")
            return

        # 保存上一次的均线值
        self.last_fast_ma = self.fast_ma
        self.last_slow_ma = self.slow_ma

        # 计算当前的均线值
        fast_ma_array = self.am.get_ma(self.fast_window)
        slow_ma_array = self.am.get_ma(self.slow_window)
        self.fast_ma = fast_ma_array[-1]
        self.slow_ma = slow_ma_array[-1]

        # 添加调试日志，帮助理解均线计算
        self.write_log(
            f"均线计算 - 当前K线：{bar.datetime}, 价格：{bar.close_price}, 快线：{self.fast_ma:.2f}, 慢线：{self.slow_ma:.2f}, 上一根快线：{self.last_fast_ma:.2f}, 上一根慢线：{self.last_slow_ma:.2f}",
            level="DEBUG")

        # 交易信号
        # 添加一个小的阈值，避免由于浮点数精度问题导致的假信号
        threshold = 0.0001

        # 确保有足够的数据进行交叉判断
        if self.am.inited and self.last_fast_ma != 0 and self.last_slow_ma != 0:
            # 金叉条件：上一根K线快线在慢线下方，当前K线快线在慢线上方，且差值大于阈值
            cross_over_condition1 = self.last_fast_ma < self.last_slow_ma
            cross_over_condition2 = self.fast_ma > self.slow_ma
            cross_over_condition3 = abs(
                self.fast_ma - self.slow_ma) > threshold
            cross_over = cross_over_condition1 and cross_over_condition2 and cross_over_condition3

            # 死叉条件：上一根K线快线在慢线上方，当前K线快线在慢线下方，且差值大于阈值
            cross_below_condition1 = self.last_fast_ma > self.last_slow_ma
            cross_below_condition2 = self.fast_ma < self.slow_ma
            cross_below_condition3 = abs(
                self.fast_ma - self.slow_ma) > threshold
            cross_below = cross_below_condition1 and cross_below_condition2 and cross_below_condition3

        else:
            # 数据不足，不产生交叉信号
            cross_over = False
            cross_below = False
            self.write_log(f"数据不足，无法判断交叉信号：{bar.datetime}", level="DEBUG")

        # 添加调试日志，帮助理解交叉信号
        if cross_over:
            self.write_log(f"检测到金叉信号：{bar.datetime}", level="DEBUG")
        elif cross_below:
            self.write_log(f"检测到死叉信号：{bar.datetime}", level="DEBUG")

        # 计算当前总持仓量和方向
        total_long = 0
        total_short = 0

        for position in self.positions:
            if position.direction == "long":
                total_long += position.volume
            else:  # direction == "short"
                total_short += position.volume

        # 更新当前持仓方向
        if total_long > 0 and total_short == 0:
            self.current_pos_direction = "long"
        elif total_short > 0 and total_long == 0:
            self.current_pos_direction = "short"
        elif total_long == 0 and total_short == 0:
            self.current_pos_direction = ""
        else:
            # 同时有多空持仓，这种情况不应该出现
            self.write_log(
                f"警告：同时存在多空持仓，多头：{total_long}，空头：{total_short}", level="WARNING")

        # 获取当前可用资金
        available_capital = self.get_available_capital()

        # 检查移动止损条件 - 在处理交叉信号前先检查是否需要止损或止盈
        positions_to_close = []  # 需要平仓的持仓

        for position in self.positions:
            # 更新持仓的最高/最低价格
            if position.direction == "long":
                # 多头持仓，更新最高价
                if bar.close_price > position.highest_price:
                    position.highest_price = bar.close_price
                    # 更新止盈价格 (开仓价格 + 止损百分比)
                    position.take_profit_price = position.price * \
                        (1 + self.stop_loss_pct / 100)
                    self.write_log(
                        f"多头更新最高价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f}", level="DEBUG")

                # 检查是否触发止损
                stop_loss_price = position.price * \
                    (1 - self.stop_loss_pct / 100)
                if bar.close_price <= stop_loss_price:
                    # 触发止损
                    positions_to_close.append(
                        (position, "止损", bar.close_price))
                    self.write_log(
                        f"多头触发止损: 当前收盘价 {bar.close_price:.4f} <= 止损价 {stop_loss_price:.4f}")
                    continue

                # 检查是否触发移动止盈
                # 只有当价格曾经超过止盈线，且当前回撤到指定比例时才触发
                if position.highest_price > position.take_profit_price:
                    # 计算回撤价格 = 止盈价 + (最高价 - 止盈价) * (1 - 回撤比例)
                    retrace_price = position.take_profit_price + \
                        (position.highest_price -
                         position.take_profit_price) * (1 - self.profit_take_ratio)
                    # 使用收盘价与回撤价比较，而不是最低价
                    if bar.close_price <= retrace_price:
                        # 触发移动止盈
                        positions_to_close.append(
                            (position, "移动止盈", bar.close_price))
                        self.write_log(
                            f"多头触发移动止盈: 当前收盘价 {bar.close_price:.4f} <= 回撤价 {retrace_price:.4f} (最高价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f})")
                        continue

            else:  # direction == "short"
                # 空头持仓，更新最低价
                if position.highest_price == 0 or bar.close_price < position.highest_price:
                    position.highest_price = bar.close_price
                    # 更新止盈价格 (开仓价格 - 止损百分比)
                    position.take_profit_price = position.price * \
                        (1 - self.stop_loss_pct / 100)
                    self.write_log(
                        f"空头更新最低价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f}", level="DEBUG")

                # 检查是否触发止损
                stop_loss_price = position.price * \
                    (1 + self.stop_loss_pct / 100)
                if bar.close_price >= stop_loss_price:
                    # 触发止损
                    positions_to_close.append(
                        (position, "止损", bar.close_price))
                    self.write_log(
                        f"空头触发止损: 当前收盘价 {bar.close_price:.4f} >= 止损价 {stop_loss_price:.4f}")
                    continue

                # 检查是否触发移动止盈
                # 只有当价格曾经低于止盈线，且当前反弹到指定比例时才触发
                if position.highest_price < position.take_profit_price:
                    # 计算回撤价格 = 止盈价 - (止盈价 - 最低价) * (1 - 回撤比例)
                    retrace_price = position.take_profit_price - \
                        (position.take_profit_price -
                         position.highest_price) * (1 - self.profit_take_ratio)
                    # 使用收盘价与回撤价比较，而不是最高价
                    if bar.close_price >= retrace_price:
                        # 触发移动止盈
                        positions_to_close.append(
                            (position, "移动止盈", bar.close_price))
                        self.write_log(
                            f"空头触发移动止盈: 当前收盘价 {bar.close_price:.4f} >= 回撤价 {retrace_price:.4f} (最低价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f})")
                        continue

        # 执行止损和止盈平仓
        for position, close_type, close_price in positions_to_close:
            # 计算平仓盈亏
            if position.direction == "long":
                profit = (close_price - position.price) * position.volume
                profit_pct = (close_price / position.price - 1) * 100
            else:  # direction == "short"
                profit = (position.price - close_price) * position.volume
                profit_pct = (position.price / close_price - 1) * 100

            # 计算平仓时的手续费和滑点
            close_commission = position.volume * \
                self.size * close_price * self.commission_rate
            open_commission = position.volume * self.size * \
                position.price * self.commission_rate

            # 滑点计算 - 使用固定点数方式
            close_slippage = position.volume * self.size * self.slippage_rate
            open_slippage = position.volume * self.size * self.slippage_rate

            # 计算总手续费和总滑点
            total_commission = open_commission + close_commission
            total_slippage = open_slippage + close_slippage

            # 计算净盈亏（包含开仓和平仓的所有成本）
            net_profit = profit - total_commission - total_slippage

            # 执行平仓
            if position.direction == "long":
                self.sell(close_price, position.volume)
                self.write_log(
                    f"{close_type}平多：{bar.datetime}, 价格：{close_price}, 数量：{position.volume}, 开仓价：{position.price:.4f}, 盈亏：{profit:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")
            else:  # direction == "short"
                self.cover(close_price, position.volume)
                self.write_log(
                    f"{close_type}平空：{bar.datetime}, 价格：{close_price}, 数量：{position.volume}, 开仓价：{position.price:.4f}, 盈亏：{profit:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

            # 更新已使用资金
            if hasattr(self, '_used_capital'):
                self._used_capital -= position.price * position.volume
                # 清除资金缓存，确保下次获取可用资金时重新计算
                if hasattr(self, '_cached_available_capital'):
                    delattr(self, '_cached_available_capital')

            # 强制重新计算可用资金
            self.get_available_capital(force_recalculate=True)

        # 从持仓列表中移除已平仓的持仓
        if positions_to_close:
            positions_to_remove = [p for p, _, _ in positions_to_close]
            self.positions = [
                p for p in self.positions if p not in positions_to_remove]
            # 更新当前持仓方向
            if not self.positions:
                self.current_pos_direction = ""
            elif all(p.direction == "long" for p in self.positions):
                self.current_pos_direction = "long"
            elif all(p.direction == "short" for p in self.positions):
                self.current_pos_direction = "short"
            else:
                self.current_pos_direction = "mixed"  # 这种情况不应该出现

            # 立即更新可用资金，确保后续操作使用最新的资金数据
            available_capital = self.get_available_capital(
                force_recalculate=True)

        # 计算开仓数量和所需资金
        margin_rate = 1.0  # 全额保证金

        # 获取当前可用资金
        available_capital = self.get_available_capital(force_recalculate=True)

        # 获取当前账户总价值（包括未实现盈亏）
        current_equity = self.get_current_equity()

        # 不再每次都记录资金状况，只在开仓时记录

        if self.order_type == "quantity":
            # 按币数量下单
            volume = self.position_size
            # 计算开仓所需资金
            required_capital = bar.close_price * volume * margin_rate
            # 计算手续费
            commission = required_capital * self.commission_rate
            # 计算滑点 - 使用固定点数方式
            slippage = volume * self.size * self.slippage_rate

            # 计算总成本
            total_cost_temp = required_capital + commission + slippage

            # 检查是否超过可用资金
            if total_cost_temp > available_capital:
                # 如果超过可用资金，按可用资金比例缩减交易量
                ratio = available_capital / total_cost_temp
                volume = volume * ratio
                # 四舍五入到4位小数
                volume = round(volume, 4)
                # 重新计算所需资金和成本
                required_capital = bar.close_price * volume * margin_rate
                commission = required_capital * self.commission_rate
                slippage = volume * self.size * self.slippage_rate

                # 不记录交易量超过可用资金的日志，只在实际开仓时记录
        elif self.order_type == "ratio":
            # 按资金比例下单
            # 计算要使用的资金金额（基于账户总价值）
            amount = current_equity * self.position_size

            # 确保不超过可用资金
            amount = min(amount, available_capital)

            # 如果资金太少，不开仓
            if amount < 1.0:
                self.write_log(
                    f"资金太少，无法按比例开仓：{bar.datetime}, 当前账户总价值：{current_equity:.2f}, 比例：{self.position_size:.4f}, 计算金额：{amount:.2f}", level="WARNING")
                volume = 0
                required_capital = 0
                commission = 0
                slippage = 0
            else:
                # 计算可以买入的币数量（考虑手续费和滑点）
                volume = amount / (bar.close_price *
                                   (1 + self.commission_rate) + self.slippage_rate)
                # 四舍五入到4位小数
                volume = round(volume, 4)
                # 计算实际所需资金
                required_capital = amount
                # 计算手续费和滑点
                commission = bar.close_price * volume * self.commission_rate
                slippage = volume * self.size * self.slippage_rate
        else:  # order_type == "amount"
            # 按金额下单
            amount = self.position_size

            # 确保不超过可用资金
            amount = min(amount, available_capital)

            # 如果资金太少，不开仓
            if amount < 1.0:
                self.write_log(
                    f"资金太少，无法按金额开仓：{bar.datetime}, 可用资金：{available_capital:.2f}, 设定金额：{self.position_size:.2f}, 实际金额：{amount:.2f}", level="WARNING")
                volume = 0
                required_capital = 0
                commission = 0
                slippage = 0
            else:
                # 计算可以买入的币数量（考虑手续费和滑点）
                # 解方程：price * volume + price * volume * commission_rate + volume * slippage_rate = amount
                # 简化为：volume * price * (1 + commission_rate) + volume * slippage_rate = amount
                # 得到：volume = amount / (price * (1 + commission_rate) + slippage_rate)
                volume = amount / (bar.close_price *
                                   (1 + self.commission_rate) + self.slippage_rate)
                # 四舍五入到4位小数
                volume = round(volume, 4)
                # 计算实际所需资金
                required_capital = amount
                # 计算手续费和滑点
                commission = bar.close_price * volume * self.commission_rate
                slippage = volume * self.size * self.slippage_rate

        # 加上手续费和滑点
        total_cost = required_capital + commission + slippage

        # 确保交易量大于0
        if volume <= 0:
            self.write_log(
                f"计算得到的交易量为0或负数，跳过开仓：{bar.datetime}", level="WARNING")
            return  # 直接返回，不执行开仓逻辑

        # 检查是否接近回测结束
        # 获取剩余K线数量（由引擎设置在bar对象中）
        remaining_bars = getattr(bar, 'remaining_bars', 0)

        # 如果剩余K线数量少于10，不开新仓
        if remaining_bars < 10:
            return  # 直接返回，不执行开仓逻辑

        # 金叉信号处理
        if cross_over:
            # 只有当前没有多仓时才处理金叉信号
            if self.current_pos_direction != "long":
                # 如果当前持有空仓，先平仓
                if self.current_pos_direction == "short":
                    # 计算总空头持仓量
                    total_short_volume = sum(
                        p.volume for p in self.positions if p.direction == "short")

                    # 计算平仓盈亏
                    avg_short_price = sum(
                        p.price * p.volume for p in self.positions if p.direction == "short") / total_short_volume
                    profit = (avg_short_price - bar.close_price) * \
                        total_short_volume
                    profit_pct = (avg_short_price / bar.close_price - 1) * 100

                    # 计算平仓时的手续费和滑点
                    close_commission = total_short_volume * self.size * \
                        bar.close_price * self.commission_rate
                    open_commission = total_short_volume * self.size * \
                        avg_short_price * self.commission_rate

                    # 滑点计算 - 使用固定点数方式
                    close_slippage = total_short_volume * self.size * self.slippage_rate
                    open_slippage = total_short_volume * self.size * self.slippage_rate

                    # 计算总手续费和总滑点
                    total_commission = open_commission + close_commission
                    total_slippage = open_slippage + close_slippage

                    # 计算净盈亏（包含开仓和平仓的所有成本）
                    net_profit = profit - total_commission - total_slippage

                    # 平仓
                    self.cover(bar.close_price, total_short_volume)

                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital -= avg_short_price * total_short_volume
                        # 清除资金缓存，确保下次获取可用资金时重新计算
                        if hasattr(self, '_cached_available_capital'):
                            delattr(self, '_cached_available_capital')

                    # 强制重新计算可用资金
                    self.get_available_capital(force_recalculate=True)

                    # 记录平仓信息
                    self.write_log(
                        f"金叉平空：{bar.datetime}, 价格：{bar.close_price}, 数量：{total_short_volume}, 平均开仓价：{avg_short_price:.2f}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

                    # 清空持仓列表
                    self.positions = [
                        p for p in self.positions if p.direction != "short"]
                    self.current_pos_direction = ""

                    # 立即更新可用资金，确保后续操作使用最新的资金数据
                    available_capital = self.get_available_capital(
                        force_recalculate=True)

                # 开多仓
                if available_capital >= total_cost:
                    # 资金充足，可以开仓
                    self.buy(bar.close_price, volume)

                    # 创建新的持仓对象并添加到持仓列表
                    new_position = Position(
                        "long", bar.close_price, volume, bar.datetime)

                    # 设置止损价格和止盈价格
                    new_position.stop_loss_price = bar.close_price * \
                        (1 - self.stop_loss_pct / 100)
                    new_position.take_profit_price = bar.close_price * \
                        (1 + self.stop_loss_pct / 100)
                    new_position.highest_price = bar.close_price  # 初始化最高价为开仓价

                    self.positions.append(new_position)

                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital += bar.close_price * volume

                    # 强制重新计算可用资金
                    self.get_available_capital(force_recalculate=True)

                    # 记录日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"金叉做多(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}, 止损价: {new_position.stop_loss_price:.4f}, 止盈价: {new_position.take_profit_price:.4f}")
                    elif self.order_type == "ratio":
                        ratio_percent = self.position_size * 100
                        current_equity = self.get_current_equity()
                        amount = current_equity * self.position_size
                        self.write_log(
                            f"金叉做多(按资金比例)：{bar.datetime}, 价格：{bar.close_price}, 比例：{ratio_percent:.2f}%, 当前账户总价值：{current_equity:.2f}, 金额：{amount:.2f}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}, 止损价: {new_position.stop_loss_price:.4f}, 止盈价: {new_position.take_profit_price:.4f}")
                    else:
                        self.write_log(
                            f"金叉做多(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}, 止损价: {new_position.stop_loss_price:.4f}, 止盈价: {new_position.take_profit_price:.4f}")
                    self.current_pos_direction = "long"
                else:
                    # 资金不足，记录到日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                    elif self.order_type == "ratio":
                        ratio_percent = self.position_size * 100
                        current_equity = self.get_current_equity()
                        amount = current_equity * self.position_size
                        self.write_log(
                            f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 比例：{ratio_percent:.2f}%, 当前账户总价值：{current_equity:.2f}, 金额：{amount:.2f}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                    else:
                        self.write_log(
                            f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")

        # 死叉信号处理
        elif cross_below:
            # 只有当前没有空仓时才处理死叉信号
            if self.current_pos_direction != "short":
                # 如果当前持有多仓，先平仓
                if self.current_pos_direction == "long":
                    # 计算总多头持仓量
                    total_long_volume = sum(
                        p.volume for p in self.positions if p.direction == "long")

                    # 计算平仓盈亏
                    avg_long_price = sum(
                        p.price * p.volume for p in self.positions if p.direction == "long") / total_long_volume
                    profit = (bar.close_price - avg_long_price) * \
                        total_long_volume
                    profit_pct = (bar.close_price / avg_long_price - 1) * 100

                    # 计算平仓时的手续费和滑点
                    close_commission = total_long_volume * self.size * \
                        bar.close_price * self.commission_rate
                    open_commission = total_long_volume * self.size * \
                        avg_long_price * self.commission_rate

                    # 滑点计算 - 使用固定点数方式
                    close_slippage = total_long_volume * self.size * self.slippage_rate
                    open_slippage = total_long_volume * self.size * self.slippage_rate

                    # 计算总手续费和总滑点
                    total_commission = open_commission + close_commission
                    total_slippage = open_slippage + close_slippage

                    # 计算净盈亏（包含开仓和平仓的所有成本）
                    net_profit = profit - total_commission - total_slippage

                    # 平仓
                    self.sell(bar.close_price, total_long_volume)

                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital -= avg_long_price * total_long_volume
                        # 清除资金缓存，确保下次获取可用资金时重新计算
                        if hasattr(self, '_cached_available_capital'):
                            delattr(self, '_cached_available_capital')

                    # 强制重新计算可用资金
                    self.get_available_capital(force_recalculate=True)

                    # 记录平仓信息
                    self.write_log(
                        f"死叉平多：{bar.datetime}, 价格：{bar.close_price}, 数量：{total_long_volume}, 平均开仓价：{avg_long_price:.2f}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

                    # 清空持仓列表
                    self.positions = [
                        p for p in self.positions if p.direction != "long"]
                    self.current_pos_direction = ""

                    # 立即更新可用资金，确保后续操作使用最新的资金数据
                    available_capital = self.get_available_capital(
                        force_recalculate=True)

                # 开空仓
                if available_capital >= total_cost:
                    # 资金充足，可以开仓
                    self.short(bar.close_price, volume)

                    # 创建新的持仓对象并添加到持仓列表
                    new_position = Position(
                        "short", bar.close_price, volume, bar.datetime)

                    # 设置止损价格和止盈价格
                    new_position.stop_loss_price = bar.close_price * \
                        (1 + self.stop_loss_pct / 100)
                    new_position.take_profit_price = bar.close_price * \
                        (1 - self.stop_loss_pct / 100)
                    new_position.highest_price = bar.close_price  # 初始化最低价为开仓价

                    self.positions.append(new_position)

                    # 更新已使用资金
                    if hasattr(self, '_used_capital'):
                        self._used_capital += bar.close_price * volume

                    # 强制重新计算可用资金
                    new_available_capital = self.get_available_capital(
                        force_recalculate=True)
                    self.write_log(
                        f"开仓后资金状况 - 可用资金: {new_available_capital:.2f}")

                    # 记录日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"死叉做空(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}, 止损价: {new_position.stop_loss_price:.4f}, 止盈价: {new_position.take_profit_price:.4f}")
                    elif self.order_type == "ratio":
                        ratio_percent = self.position_size * 100
                        current_equity = self.get_current_equity()
                        amount = current_equity * self.position_size
                        self.write_log(
                            f"死叉做空(按资金比例)：{bar.datetime}, 价格：{bar.close_price}, 比例：{ratio_percent:.2f}%, 当前账户总价值：{current_equity:.2f}, 金额：{amount:.2f}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}, 止损价: {new_position.stop_loss_price:.4f}, 止盈价: {new_position.take_profit_price:.4f}")
                    else:
                        self.write_log(
                            f"死叉做空(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, MA{self.fast_window}: {self.fast_ma:.2f}, MA{self.slow_window}: {self.slow_ma:.2f}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}, 止损价: {new_position.stop_loss_price:.4f}, 止盈价: {new_position.take_profit_price:.4f}")
                    self.current_pos_direction = "short"
                else:
                    # 资金不足，记录到日志
                    if self.order_type == "quantity":
                        self.write_log(
                            f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                    elif self.order_type == "ratio":
                        ratio_percent = self.position_size * 100
                        current_equity = self.get_current_equity()
                        amount = current_equity * self.position_size
                        self.write_log(
                            f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 比例：{ratio_percent:.2f}%, 当前账户总价值：{current_equity:.2f}, 金额：{amount:.2f}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                    else:
                        self.write_log(
                            f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
