"""
Utility functions for backtesting
"""

from typing import Any, Dict, List, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from .constant import Direction, Exchange, Interval
from vnpy_backtester.objects.object import BarData, TickData


def extract_vt_symbol(vt_symbol: str) -> Tuple[str, Exchange]:
    """
    Extract symbol and exchange from vt_symbol.
    """
    symbol, exchange_str = vt_symbol.split(".")
    return symbol, Exchange(exchange_str)


def round_to(value: float, target: float) -> float:
    """
    Round price to price tick value.
    """
    rounded = int(round(value / target)) * target
    return rounded


def generate_bar_from_ticks(
    ticks: List[TickData],
    interval: Interval
) -> List[BarData]:
    """
    Generate bar data from tick data.
    """
    bars: List[BarData] = []

    for tick in ticks:
        # Generate timestamp for bar
        timestamp = tick.datetime.replace(second=0, microsecond=0)

        if interval == Interval.MINUTE:
            pass
        elif interval == Interval.HOUR:
            timestamp = timestamp.replace(minute=0)
        elif interval == Interval.DAILY:
            timestamp = timestamp.replace(hour=0, minute=0)
        else:
            raise ValueError(f"Unsupported interval: {interval}")

        # If not the first tick, check if new bar needed
        if bars and timestamp == bars[-1].datetime:
            # Update existing bar
            bar = bars[-1]
            bar.high_price = max(bar.high_price, tick.last_price)
            bar.low_price = min(bar.low_price, tick.last_price)
            bar.close_price = tick.last_price
            bar.volume += tick.volume
            bar.turnover += tick.turnover
            bar.open_interest = tick.open_interest
        else:
            # Create new bar
            bar = BarData(
                symbol=tick.symbol,
                exchange=tick.exchange,
                datetime=timestamp,
                interval=interval,
                open_price=tick.last_price,
                high_price=tick.last_price,
                low_price=tick.last_price,
                close_price=tick.last_price,
                volume=tick.volume,
                turnover=tick.turnover,
                open_interest=tick.open_interest,
                gateway_name=tick.gateway_name
            )
            bars.append(bar)

    return bars


def load_bar_data_from_csv(
    file_path: str,
    symbol: str,
    exchange: Exchange,
    interval: Interval,
    datetime_format: str = "%Y-%m-%d %H:%M:%S"
) -> List[BarData]:
    """
    Load bar data from csv file.
    """
    df = pd.read_csv(file_path)
    bars = []

    for _, row in df.iterrows():
        bar = BarData(
            symbol=symbol,
            exchange=exchange,
            interval=interval,
            datetime=datetime.strptime(row["datetime"], datetime_format),
            open_price=float(row["open"]),
            high_price=float(row["high"]),
            low_price=float(row["low"]),
            close_price=float(row["close"]),
            volume=float(row["volume"]),
            turnover=float(row.get("turnover", 0)),
            open_interest=float(row.get("open_interest", 0)),
            gateway_name="CSV"
        )
        bars.append(bar)

    return bars


def calculate_statistics(
    daily_results: Dict[datetime, Any],
    capital: float,
    annual_days: int = 365,
    engine=None  # 添加引擎参数
) -> Dict[str, Any]:
    """
    Calculate statistics from daily results.
    """
    if not daily_results:
        return {}

    # Get daily pnl and return data
    daily_pnl = []
    daily_return = []

    # 获取所有日期并排序
    dates = sorted(daily_results.keys())
    for date in dates:
        daily_result = daily_results[date]
        daily_pnl.append(daily_result.net_pnl)
        daily_return.append(daily_result.net_pnl / capital)

    # Calculate statistics values
    total_days = len(daily_pnl)
    profit_days = len([pnl for pnl in daily_pnl if pnl > 0])
    loss_days = len([pnl for pnl in daily_pnl if pnl < 0])

    end_balance = capital + sum(daily_pnl)
    total_return = (end_balance / capital - 1) * 100
    annual_return = total_return / total_days * annual_days

    daily_return_array = np.array(daily_return)
    return_std = daily_return_array.std() * np.sqrt(annual_days)

    # 初始化夏普比率为0，稍后再计算
    sharpe_ratio = 0

    # 将回撤计算移到后面，等待trade_pnls计算完成后再计算

    # Calculate trade statistics
    # 收集所有交易的盈亏数据
    trade_pnls = []

    # 从daily_results中提取所有交易
    all_trades = []
    for daily_result in daily_results.values():
        if hasattr(daily_result, 'trade_list'):
            all_trades.extend(daily_result.trade_list)

    # 不输出交易数量，减少终端输出
    # print(f"总交易记录数量: {len(all_trades)}")

    # 我们需要根据信号来计算交易次数和盈亏
    # 在回测中，信号是通过bar.signals属性传递的
    # 我们需要统计有多少个非零信号，这就是实际的交易次数

    # 获取所有K线数据中的信号
    signal_count = 0
    signal_pnls = []

    # 如果引擎参数存在，使用引擎的历史数据
    if engine and hasattr(engine, 'history_data') and engine.history_data:
        # 获取历史数据
        bars = engine.history_data

        # 预处理：创建一个字典，将每个K线的datetime映射到其索引
        # 这样可以在O(1)时间内找到下一个K线，而不是O(n)
        bar_indices = {}
        for i, bar in enumerate(bars):
            if hasattr(bar, 'datetime'):
                bar_indices[bar.datetime] = i

        # 预处理：找出所有有信号的K线
        signal_bars = []
        for bar in bars:
            if hasattr(bar, 'signals') and bar.signals != 0:
                signal_bars.append(bar)

        # 统计信号数量
        signal_count = len(signal_bars)

        # 计算每个信号的盈亏
        for bar in signal_bars:
            # 获取当前K线的收盘价
            current_close = bar.close_price

            # 获取当前K线索引（高效方法）
            current_idx = bar_indices.get(bar.datetime, -1)

            # 根据信号计算盈亏
            # 获取策略的实际下单数量
            # 如果引擎有策略属性，并且策略有position_size属性，则使用策略的position_size
            actual_volume = 1  # 默认值
            if engine and hasattr(engine, 'strategy') and engine.strategy:
                if hasattr(engine.strategy, 'position_size'):
                    # 根据策略的order_type确定如何计算交易量
                    if hasattr(engine.strategy, 'order_type') and engine.strategy.order_type == "amount":
                        # 按金额下单，需要计算实际数量
                        if hasattr(engine.strategy, 'calculate_volume_by_amount'):
                            actual_volume = engine.strategy.calculate_volume_by_amount(
                                current_close, engine.strategy.position_size)
                    else:
                        # 按数量下单，直接使用position_size
                        actual_volume = engine.strategy.position_size

            # 查找平仓价格
            # 在策略中，通常会在holding_bars个K线后平仓
            # 我们需要找到平仓的K线
            close_price = current_close  # 默认使用当前K线收盘价
            holding_bars = 96  # 默认持仓K线数

            # 从策略中获取持仓K线数
            if engine and hasattr(engine, 'strategy') and engine.strategy:
                if hasattr(engine.strategy, 'holding_bars'):
                    holding_bars = engine.strategy.holding_bars

            # 尝试找到平仓K线
            close_idx = current_idx + holding_bars
            if close_idx < len(bars):
                close_price = bars[close_idx].close_price

            if bar.signals == 1:  # 做多信号
                # 多头盈亏 = (平仓价格 - 开仓价格) * 实际交易量
                signal_pnl = (close_price - current_close) * actual_volume
            elif bar.signals == -1:  # 做空信号
                # 空头盈亏 = (开仓价格 - 平仓价格) * 实际交易量
                signal_pnl = (current_close - close_price) * actual_volume
            else:  # 无信号
                signal_pnl = 0

            signal_pnls.append(signal_pnl)

        # 不输出信号统计，减少终端输出
        # print(f"根据信号统计，总交易次数: {signal_count}")
    else:
        # 如果没有引擎参数或历史数据，尝试从daily_results中提取信号
        # 不输出警告，减少终端输出
        # print("警告：未找到引擎历史数据，尝试从daily_results中提取信号")
        pass

    # 如果没有找到信号，使用交易记录作为备选方案
    if signal_count == 0:
        # 不输出警告，减少终端输出
        # print("警告：未找到信号，使用交易记录作为备选方案")
        pass
        # 直接计算每个交易的盈亏
        trade_pnls = []

        # 预处理：创建一个字典，将日期映射到收盘价
        # 这样可以在O(1)时间内找到收盘价，而不是O(n)
        date_to_close = {}
        for date, dr in daily_results.items():
            if hasattr(dr, 'date') and hasattr(dr, 'close_price'):
                date_to_close[dr.date] = dr.close_price

        for trade in all_trades:
            # 如果交易对象有盈亏属性，直接使用
            if hasattr(trade, 'pnl') and trade.pnl is not None:
                trade_pnls.append(trade.pnl)
            # 否则，我们需要计算盈亏
            else:
                # 获取交易日期
                trade_date = trade.datetime.date()

                # 获取当天的收盘价（高效方法）
                close_price = date_to_close.get(trade_date, 0)

                # 计算盈亏
                if trade.direction == Direction.LONG:
                    # 多头：收盘价 - 买入价
                    pnl = (close_price - trade.price) * trade.volume
                else:
                    # 空头：卖出价 - 收盘价
                    pnl = (trade.price - close_price) * trade.volume

                trade_pnls.append(pnl)
    else:
        # 使用信号产生的盈亏
        trade_pnls = signal_pnls
        # 只输出交易次数，不输出盈亏列表
        print(f"根据信号计算，总交易次数: {signal_count}")

    # 计算交易胜率和盈亏比
    # 过滤掉接近0的盈亏（可能是由于计算误差）
    epsilon = 1e-10
    filtered_trade_pnls = [pnl for pnl in trade_pnls if abs(pnl) > epsilon]

    # 分离盈利和亏损交易
    winning_trades = [pnl for pnl in filtered_trade_pnls if pnl > 0]
    losing_trades = [pnl for pnl in filtered_trade_pnls if pnl < 0]

    # 计算交易数量
    total_trades = len(filtered_trade_pnls)
    winning_trades_count = len(winning_trades)
    losing_trades_count = len(losing_trades)

    # 计算胜率
    win_rate = winning_trades_count / total_trades if total_trades else 0

    # 计算平均盈利和平均亏损
    avg_winning = sum(winning_trades) / \
        winning_trades_count if winning_trades_count else 0
    # 亏损交易的值是负数，取绝对值使平均亏损为正数
    avg_losing_abs = abs(sum(losing_trades) /
                         losing_trades_count) if losing_trades_count else 0

    # 计算盈亏比（平均盈利/平均亏损的绝对值）
    # 这里不需要再取绝对值，因为avg_losing_abs已经是正数
    profit_ratio = avg_winning / \
        avg_losing_abs if avg_losing_abs else float('inf')

    # 计算利润因子（总盈利/总亏损的绝对值）
    profit_factor = abs(sum(winning_trades) / sum(losing_trades)
                        ) if sum(losing_trades) else float('inf')

    # 计算期望收益（胜率 * 平均盈利 + (1-胜率) * 平均亏损）
    # 注意：这里使用负的avg_losing_abs，因为亏损是负值
    expected_return = win_rate * avg_winning - (1 - win_rate) * avg_losing_abs

    # 计算总盈亏
    total_pnl = sum(filtered_trade_pnls)

    # 计算夏普比率
    # 使用年化收益率除以年化标准差的方式计算
    # 不考虑无风险利率，直接使用年化收益率除以年化标准差
    if return_std > 0:
        sharpe_ratio = annual_return / (return_std * 100)
    else:
        # 如果标准差为0，夏普比率为inf
        sharpe_ratio = float('inf')

    # Calculate drawdown
    # 使用与图表引擎相同的方法计算回撤
    drawdowns = []

    # 计算每日资金余额
    balance = [capital + sum(daily_pnl[:i+1]) for i in range(len(daily_pnl))]

    # 计算回撤
    max_balance = capital  # 从初始资金开始
    for bal in balance:
        max_balance = max(max_balance, bal)
        drawdown = (max_balance - bal) / max_balance * \
            100 if max_balance > 0 else 0
        drawdowns.append(drawdown)

    max_drawdown = max(drawdowns) if drawdowns else 0

    max_ddpercent = max_drawdown

    # Output
    if not daily_results:
        # 如果没有交易，返回默认统计信息
        statistics = {
            "start_date": datetime.now().date(),
            "end_date": datetime.now().date(),
            "total_days": 0,
            "profit_days": 0,
            "loss_days": 0,
            "capital": capital,
            "end_balance": capital,
            "max_drawdown": 0.0,
            "max_ddpercent": 0.0,
            "total_return": 0.0,
            "annual_return": 0.0,
            "daily_return": 0.0,
            "return_std": 0.0,
            "sharpe_ratio": 0.0,
            "return_drawdown_ratio": 0.0,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "profit_ratio": 0.0,  # 盈亏比（平均盈利/平均亏损的绝对值）
            "expected_return": 0.0,  # 期望收益
            "avg_winning": 0.0,
            "avg_losing": 0.0,  # 保持为0
            "total_trade_pnl": 0.0  # 总交易盈亏
        }
    else:
        # 获取日期并确保它们是正确的日期格式
        dates = sorted(daily_results.keys())
        start_date = dates[0]
        end_date = dates[-1]

        # 确保日期是datetime.date类型
        if hasattr(start_date, 'date'):
            start_date = start_date.date()
        if hasattr(end_date, 'date'):
            end_date = end_date.date()

        statistics = {
            "start_date": start_date,
            "end_date": end_date,
            "total_days": total_days,
            "profit_days": profit_days,
            "loss_days": loss_days,
            "capital": capital,
            "end_balance": end_balance,
            "max_drawdown": max_drawdown,
            "max_ddpercent": max_ddpercent,
            "total_return": total_return,
            "annual_return": annual_return,
            "daily_return": total_return / total_days if total_days else 0,
            "return_std": return_std,
            "sharpe_ratio": sharpe_ratio,
            "return_drawdown_ratio": (annual_return / max_ddpercent) if max_ddpercent else 0,
            "total_trades": total_trades,
            "winning_trades": winning_trades_count,
            "losing_trades": losing_trades_count,
            "win_rate": win_rate * 100,  # 转换为百分比
            "profit_factor": profit_factor,
            "profit_ratio": profit_ratio,  # 盈亏比（平均盈利/平均亏损的绝对值）
            "expected_return": expected_return,  # 期望收益
            "avg_winning": avg_winning,
            "avg_losing": -avg_losing_abs,  # 转换回负值，保持一致性
            "total_trade_pnl": total_pnl  # 总交易盈亏
        }

    return statistics
