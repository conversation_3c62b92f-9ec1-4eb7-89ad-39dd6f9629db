"""
Event-related classes and functions for event-driven architecture.
Enhanced for vnpy_ctabacktester compatibility.
"""

from enum import Enum
from queue import Queue, Empty
from threading import Thread
from time import sleep
from typing import Any, Callable, List, Optional, Dict
from datetime import datetime
import traceback

# 定义事件类型


class EventType(Enum):
    """
    Event types used in the event-driven architecture.
    Enhanced with additional event types for comprehensive backtesting.
    """
    # 基础事件
    EVENT_TICK = "eTick."
    EVENT_BAR = "eBar."
    EVENT_ORDER = "eOrder."
    EVENT_TRADE = "eTrade."
    EVENT_POSITION = "ePosition."
    EVENT_ACCOUNT = "eAccount."
    EVENT_CONTRACT = "eContract."
    EVENT_LOG = "eLog."
    EVENT_TIMER = "eTimer."
    EVENT_INIT = "eInit."
    EVENT_START = "eStart."
    EVENT_STOP = "eStop."
    EVENT_STOP_ORDER = "eStopOrder."

    # 回测专用事件
    EVENT_BACKTESTING_START = "eBacktestingStart."
    EVENT_BACKTESTING_STOP = "eBacktestingStop."
    EVENT_BACKTESTING_FINISHED = "eBacktestingFinished."
    EVENT_DAILY_RESULT = "eDailyResult."
    EVENT_PORTFOLIO_UPDATE = "ePortfolioUpdate."
    EVENT_RISK_CHECK = "eRiskCheck."
    EVENT_STRATEGY_UPDATE = "eStrategyUpdate."
    EVENT_PNL_UPDATE = "ePnlUpdate."
    EVENT_DRAWDOWN_UPDATE = "eDrawdownUpdate."

    # 数据事件
    EVENT_DATA_UPDATE = "eDataUpdate."
    EVENT_SIGNAL_GENERATED = "eSignalGenerated."
    EVENT_POSITION_OPENED = "ePositionOpened."
    EVENT_POSITION_CLOSED = "ePositionClosed."


class Event:
    """
    Event object contains information that will be processed by event engine.
    Enhanced with additional metadata for better event tracking.
    """

    def __init__(self, type: EventType, data: Any = None, source: str = None, timestamp: datetime = None) -> None:
        """
        Initialize an Event.

        Parameters:
            type: Event type
            data: Event data object
            source: Event source (e.g., strategy name, engine name)
            timestamp: Event timestamp
        """
        self.type: EventType = type
        self.data: Any = data
        self.source: str = source or "Unknown"
        self.timestamp: datetime = timestamp or datetime.now()

    def __str__(self) -> str:
        """String representation of the event."""
        return f"Event(type={self.type.value}, source={self.source}, timestamp={self.timestamp})"

    def __repr__(self) -> str:
        """Detailed representation of the event."""
        return f"Event(type={self.type.value}, data={self.data}, source={self.source}, timestamp={self.timestamp})"


# 定义处理器类型
HandlerType = Callable[[Event], None]


class EventEngine:
    """
    Event engine distributes event objects based on their type.
    Enhanced for better performance and debugging capabilities.

    It is the core of the event-driven architecture.
    """

    def __init__(self, interval: int = 1) -> None:
        """
        Initialize an EventEngine.

        Parameters:
            interval: Event processing interval in milliseconds
        """
        self._interval: int = interval
        self._queue: Queue = Queue()
        self._active: bool = False
        self._thread: Thread = Thread(target=self._run)
        self._timer: Thread = Thread(target=self._run_timer)
        self._handlers: Dict[EventType, List[Callable]] = {}
        self._general_handlers: List[Callable] = []

        # 增强功能
        self._event_count: int = 0
        self._error_count: int = 0
        self._last_error: Optional[str] = None
        self._debug_mode: bool = False

    def _run(self) -> None:
        """
        Run event processing loop.
        Enhanced with error handling and statistics.
        """
        while self._active:
            try:
                event = self._queue.get(block=True, timeout=0.1)
                self._process(event)
                self._event_count += 1
            except Empty:
                pass
            except Exception as e:
                self._error_count += 1
                self._last_error = f"Event processing error: {str(e)}\n{traceback.format_exc()}"
                if self._debug_mode:
                    print(self._last_error)

    def _process(self, event: Event) -> None:
        """
        Process an event.
        Enhanced with better error handling and debugging.
        """
        if self._debug_mode:
            print(f"Processing event: {event}")

        # 处理特定类型的事件处理器
        if event.type in self._handlers:
            for handler in self._handlers[event.type]:
                try:
                    handler(event)
                except Exception as e:
                    self._error_count += 1
                    error_msg = f"Handler error for {event.type.value}: {str(e)}\n{traceback.format_exc()}"
                    self._last_error = error_msg
                    if self._debug_mode:
                        print(error_msg)

        # 处理通用事件处理器
        for handler in self._general_handlers:
            try:
                handler(event)
            except Exception as e:
                self._error_count += 1
                error_msg = f"General handler error: {str(e)}\n{traceback.format_exc()}"
                self._last_error = error_msg
                if self._debug_mode:
                    print(error_msg)

    def _run_timer(self) -> None:
        """
        Run timer event loop.
        """
        while self._active:
            sleep(self._interval / 1000)
            event = Event(EventType.EVENT_TIMER)
            self.put(event)

    def start(self) -> None:
        """
        Start event engine.
        """
        self._active = True
        self._thread.start()
        self._timer.start()

    def stop(self) -> None:
        """
        Stop event engine.
        """
        self._active = False
        self._timer.join()
        self._thread.join()

    def put(self, event: Event) -> None:
        """
        Put an event into event queue.
        For backtesting, process the event immediately to ensure synchronous behavior.
        """
        # 在回测环境中，直接处理事件，而不是放入队列
        self._process(event)

    def register(self, type: EventType, handler: HandlerType) -> None:
        """
        Register a handler for a specific event type.
        """
        handler_list = self._handlers.setdefault(type, [])
        if handler not in handler_list:
            handler_list.append(handler)

    def unregister(self, type: EventType, handler: HandlerType) -> None:
        """
        Unregister a handler for a specific event type.
        """
        handler_list = self._handlers.get(type, [])
        if handler in handler_list:
            handler_list.remove(handler)
        if not handler_list:
            self._handlers.pop(type, None)

    def register_general(self, handler: HandlerType) -> None:
        """
        Register a handler for all event types.
        """
        if handler not in self._general_handlers:
            self._general_handlers.append(handler)

    def unregister_general(self, handler: HandlerType) -> None:
        """
        Unregister a general handler.
        """
        if handler in self._general_handlers:
            self._general_handlers.remove(handler)

    def put(self, event: Event) -> None:
        """
        Put an event into the queue.
        """
        self._queue.put(event)

    def set_debug_mode(self, debug: bool) -> None:
        """
        Set debug mode for detailed event processing logs.
        """
        self._debug_mode = debug

    def get_statistics(self) -> Dict[EventType, int]:
        """
        Get event engine statistics.
        """
        return {
            "event_count": self._event_count,
            "error_count": self._error_count,
            "last_error": self._last_error,
            "active": self._active,
            "queue_size": self._queue.qsize(),
            "handler_types": list(self._handlers.keys()),
            "general_handlers_count": len(self._general_handlers)
        }

    def clear_statistics(self) -> None:
        """
        Clear event engine statistics.
        """
        self._event_count = 0
        self._error_count = 0
        self._last_error = None
