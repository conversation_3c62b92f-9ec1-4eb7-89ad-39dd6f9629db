"""
Legacy Compatibility Wrapper.
This module provides backward compatibility for existing scripts and strategies.
"""

from typing import Dict, List, Optional, Any, Type, Callable
from datetime import datetime
import warnings

from vnpy_backtester.managers.backtest_manager import BacktestManager
from vnpy_backtester.engines.engine import BacktestingEngine as NewBacktestingEngine
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.utils.constant import Interval, BacktestingMode
from vnpy_backtester.objects.object import BarData


class LegacyBacktestingEngine:
    """
    向后兼容的回测引擎包装器
    
    保持与原有API的兼容性，内部使用新的事件驱动架构
    """
    
    def __init__(self):
        """初始化兼容性包装器"""
        self._manager = BacktestManager()
        self._engine = self._manager.backtesting_engine
        
        # 兼容性标志
        self._legacy_mode = True
        
        # 发出兼容性警告
        warnings.warn(
            "You are using the legacy BacktestingEngine. "
            "Consider migrating to the new BacktestManager for better performance and features.",
            DeprecationWarning,
            stacklevel=2
        )
        
    # 保持原有的属性访问方式
    @property
    def vt_symbol(self) -> str:
        return self._engine.vt_symbol
        
    @vt_symbol.setter
    def vt_symbol(self, value: str):
        self._engine.vt_symbol = value
        
    @property
    def symbol(self) -> str:
        return self._engine.symbol
        
    @symbol.setter
    def symbol(self, value: str):
        self._engine.symbol = value
        
    @property
    def exchange(self):
        return self._engine.exchange
        
    @exchange.setter
    def exchange(self, value):
        self._engine.exchange = value
        
    @property
    def start(self) -> datetime:
        return self._engine.start
        
    @start.setter
    def start(self, value: datetime):
        self._engine.start = value
        
    @property
    def end(self) -> datetime:
        return self._engine.end
        
    @end.setter
    def end(self, value: datetime):
        self._engine.end = value
        
    @property
    def rate(self) -> float:
        return self._engine.rate
        
    @rate.setter
    def rate(self, value: float):
        self._engine.rate = value
        
    @property
    def slippage(self) -> float:
        return self._engine.slippage
        
    @slippage.setter
    def slippage(self, value: float):
        self._engine.slippage = value
        
    @property
    def size(self) -> float:
        return self._engine.size
        
    @size.setter
    def size(self, value: float):
        self._engine.size = value
        
    @property
    def pricetick(self) -> float:
        return self._engine.pricetick
        
    @pricetick.setter
    def pricetick(self, value: float):
        self._engine.pricetick = value
        
    @property
    def capital(self) -> int:
        return self._engine.capital
        
    @capital.setter
    def capital(self, value: int):
        self._engine.capital = value
        
    @property
    def mode(self) -> BacktestingMode:
        return self._engine.mode
        
    @mode.setter
    def mode(self, value: BacktestingMode):
        self._engine.mode = value
        
    # 保持原有的方法接口
    def set_parameters(self,
                      vt_symbol: str,
                      interval: Interval,
                      start: datetime,
                      end: datetime,
                      rate: float = 0.0,
                      slippage: float = 0.0,
                      size: float = 1.0,
                      pricetick: float = 0.0,
                      capital: int = 1_000_000,
                      mode: BacktestingMode = BacktestingMode.BAR,
                      **kwargs):
        """设置回测参数（兼容性方法）"""
        # 使用新的管理器初始化
        self._manager.initialize(
            vt_symbol=vt_symbol,
            start=start,
            end=end,
            rate=rate,
            slippage=slippage,
            size=size,
            pricetick=pricetick,
            capital=capital,
            mode=mode,
            interval=interval,
            **kwargs
        )
        
    def add_strategy(self, strategy_class: Type[StrategyTemplate], setting: Dict[str, Any]) -> str:
        """添加策略（兼容性方法）"""
        return self._manager.add_strategy(strategy_class, setting)
        
    def load_data(self):
        """加载数据（兼容性方法）"""
        return self._manager.load_data()
        
    def run_backtesting(self) -> Dict[str, Any]:
        """运行回测（兼容性方法）"""
        return self._manager.run_backtesting()
        
    def calculate_result(self) -> Dict[str, Any]:
        """计算结果（兼容性方法）"""
        return self._manager.get_statistics()
        
    def calculate_statistics(self, df: Any = None, output: bool = True) -> Dict[str, Any]:
        """计算统计（兼容性方法）"""
        stats = self._manager.get_statistics()
        if output:
            self._print_statistics(stats)
        return stats
        
    def show_chart(self, df: Any = None):
        """显示图表（兼容性方法）"""
        self._manager.show_chart()
        
    def get_all_trades(self) -> List[Dict[str, Any]]:
        """获取所有交易（兼容性方法）"""
        return self._manager.get_trades()
        
    def get_all_orders(self) -> List[Any]:
        """获取所有订单（兼容性方法）"""
        return self._engine.get_all_orders()
        
    def get_all_daily_results(self) -> List[Any]:
        """获取所有日结果（兼容性方法）"""
        return self._engine.get_all_daily_results()
        
    # 新增的便利方法
    def get_manager(self) -> BacktestManager:
        """获取底层的BacktestManager实例"""
        return self._manager
        
    def get_new_engine(self) -> NewBacktestingEngine:
        """获取新的回测引擎实例"""
        return self._engine
        
    def enable_debug_mode(self, debug: bool = True):
        """启用调试模式"""
        self._manager.set_debug_mode(debug)
        
    def _print_statistics(self, stats: Dict[str, Any]):
        """打印统计信息（兼容性方法）"""
        print("\n" + "="*50)
        print("回测统计结果")
        print("="*50)
        
        print(f"开始日期：{stats.get('start_date', 'N/A')}")
        print(f"结束日期：{stats.get('end_date', 'N/A')}")
        print(f"总天数：{stats.get('total_days', 0)}")
        print(f"初始资金：{stats.get('capital', 0):,.2f}")
        print(f"结束余额：{stats.get('end_balance', 0):,.2f}")
        print(f"总收益率：{stats.get('total_return', 0):.2f}%")
        print(f"年化收益率：{stats.get('annual_return', 0):.2f}%")
        print(f"最大回撤：{stats.get('max_drawdown', 0):.2f}%")
        print(f"夏普比率：{stats.get('sharpe_ratio', 0):.2f}")
        
        print(f"\n总交易次数：{stats.get('total_trades', 0)}")
        print(f"盈利交易：{stats.get('winning_trades', 0)}")
        print(f"亏损交易：{stats.get('losing_trades', 0)}")
        print(f"胜率：{stats.get('win_rate', 0):.2f}%")
        print(f"盈亏比：{stats.get('profit_ratio', 0):.2f}")
        print(f"利润因子：{stats.get('profit_factor', 0):.2f}")
        
        print(f"\n总盈亏：{stats.get('total_pnl', 0):,.2f}")
        print(f"总手续费：{stats.get('total_commission', 0):,.2f}")
        print(f"总滑点：{stats.get('total_slippage', 0):,.2f}")
        print(f"净盈亏：{stats.get('total_net_pnl', 0):,.2f}")
        
        print("="*50)
        
    def __getattr__(self, name):
        """属性代理，确保兼容性"""
        if hasattr(self._engine, name):
            return getattr(self._engine, name)
        elif hasattr(self._manager, name):
            return getattr(self._manager, name)
        else:
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")


# 为了完全向后兼容，创建一个别名
BacktestingEngine = LegacyBacktestingEngine


def create_legacy_engine() -> LegacyBacktestingEngine:
    """创建兼容性回测引擎的工厂函数"""
    return LegacyBacktestingEngine()


def create_new_manager() -> BacktestManager:
    """创建新回测管理器的工厂函数"""
    return BacktestManager()


# 迁移指南
MIGRATION_GUIDE = """
迁移指南：从旧版BacktestingEngine到新版BacktestManager

1. 基本用法迁移：
   旧版：
   engine = BacktestingEngine()
   engine.set_parameters(...)
   engine.add_strategy(...)
   engine.load_data()
   engine.run_backtesting()
   
   新版：
   manager = BacktestManager()
   manager.initialize(...)
   manager.add_strategy(...)
   manager.load_data()
   manager.run_backtesting()

2. 获取结果迁移：
   旧版：
   stats = engine.calculate_statistics()
   engine.show_chart()
   
   新版：
   results = manager.get_results()
   stats = manager.get_statistics()
   manager.show_chart()

3. 新功能：
   - 统一的PnL计算
   - 事件驱动架构
   - 更好的错误处理
   - 调试模式
   - 上下文管理器支持

4. 使用上下文管理器：
   with BacktestManager() as manager:
       manager.initialize(...)
       # 自动清理资源
"""


def print_migration_guide():
    """打印迁移指南"""
    print(MIGRATION_GUIDE)
