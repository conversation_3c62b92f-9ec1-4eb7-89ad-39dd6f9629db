"""
Event-Driven Backtesting Example
This example demonstrates the new event-driven architecture with unified PnL calculation.
"""

from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vnpy_backtester import BacktestManager, MultiPositionStrategy
from vnpy_backtester.utils.constant import Interval, BacktestingMode


def run_new_architecture_example():
    """使用新的事件驱动架构运行回测示例"""
    print("="*60)
    print("新架构回测示例 - 事件驱动 + 统一PnL计算")
    print("="*60)
    
    # 使用上下文管理器确保资源正确清理
    with BacktestManager() as manager:
        # 设置调试模式
        manager.set_debug_mode(False)
        
        # 初始化回测参数
        manager.initialize(
            vt_symbol="BTCUSDT.BINANCE",
            start=datetime(2023, 1, 1),
            end=datetime(2023, 12, 31),
            rate=0.0003,
            slippage=0.0,
            size=1.0,
            pricetick=0.01,
            capital=100000,
            mode=BacktestingMode.BAR,
            interval=Interval.HOUR,
            annual_days=365,
            risk_free=0.03
        )
        
        # 添加策略
        strategy_setting = {
            "class_name": "MultiPositionStrategy",
            "vt_symbol": "BTCUSDT.BINANCE",
            "fast_window": 10,
            "slow_window": 20,
            "max_positions": 5,
            "position_size": 0.1
        }
        
        strategy_name = manager.add_strategy(MultiPositionStrategy, strategy_setting)
        print(f"策略已添加: {strategy_name}")
        
        # 加载数据（这里假设数据已经存在）
        try:
            success = manager.load_data()
            if not success:
                print("数据加载失败，使用模拟数据")
                return
        except Exception as e:
            print(f"数据加载异常: {e}")
            return
            
        # 运行回测
        print("\n开始运行回测...")
        try:
            backtest_result = manager.run_backtesting()
            print("回测完成!")
            
            # 获取完整结果
            results = manager.get_results()
            
            # 显示统计结果
            print_statistics(results["statistics"])
            
            # 显示图表统计
            print_chart_statistics(results["chart_statistics"])
            
            # 显示交易汇总
            print_trade_summary(results["trade_summary"])
            
            # 显示图表
            manager.show_chart(
                title="新架构回测结果 - 统一PnL计算",
                save_path="charts/new_architecture_result.html"
            )
            
            # 显示引擎统计
            print_engine_statistics(results["engine_statistics"])
            
        except Exception as e:
            print(f"回测运行失败: {e}")
            import traceback
            traceback.print_exc()


def run_legacy_compatibility_example():
    """使用兼容性包装器运行回测示例"""
    print("\n" + "="*60)
    print("兼容性示例 - 使用旧版API接口")
    print("="*60)
    
    # 导入兼容性包装器
    from vnpy_backtester import BacktestingEngine
    
    # 创建引擎（内部使用新架构）
    engine = BacktestingEngine()
    
    # 使用旧版API设置参数
    engine.set_parameters(
        vt_symbol="BTCUSDT.BINANCE",
        interval=Interval.HOUR,
        start=datetime(2023, 1, 1),
        end=datetime(2023, 12, 31),
        rate=0.0003,
        slippage=0.0,
        size=1.0,
        pricetick=0.01,
        capital=100000,
        mode=BacktestingMode.BAR
    )
    
    # 添加策略
    strategy_setting = {
        "class_name": "MultiPositionStrategy",
        "vt_symbol": "BTCUSDT.BINANCE",
        "fast_window": 10,
        "slow_window": 20,
        "max_positions": 5,
        "position_size": 0.1
    }
    
    engine.add_strategy(MultiPositionStrategy, strategy_setting)
    
    # 加载数据
    try:
        engine.load_data()
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
        
    # 运行回测
    try:
        engine.run_backtesting()
        
        # 计算统计（使用旧版API）
        stats = engine.calculate_statistics(output=True)
        
        # 显示图表（使用旧版API）
        engine.show_chart()
        
        # 获取底层的新管理器（展示新功能）
        manager = engine.get_manager()
        print(f"\n底层管理器类型: {type(manager).__name__}")
        
        # 获取新引擎的统计信息
        new_stats = manager.get_statistics()
        print(f"新架构统计 - 总交易数: {new_stats.get('total_trades', 0)}")
        print(f"新架构统计 - 净盈亏: {new_stats.get('total_net_pnl', 0):,.2f}")
        
    except Exception as e:
        print(f"兼容性回测失败: {e}")
        import traceback
        traceback.print_exc()


def print_statistics(stats):
    """打印统计结果"""
    print("\n" + "="*50)
    print("统计结果 (使用统一PnL计算)")
    print("="*50)
    
    print(f"开始日期: {stats.get('start_date', 'N/A')}")
    print(f"结束日期: {stats.get('end_date', 'N/A')}")
    print(f"总天数: {stats.get('total_days', 0)}")
    print(f"初始资金: {stats.get('capital', 0):,.2f}")
    print(f"结束余额: {stats.get('end_balance', 0):,.2f}")
    print(f"总收益率: {stats.get('total_return', 0):.2f}%")
    print(f"年化收益率: {stats.get('annual_return', 0):.2f}%")
    print(f"最大回撤: {stats.get('max_drawdown', 0):.2f}%")
    print(f"夏普比率: {stats.get('sharpe_ratio', 0):.2f}")
    
    print(f"\n总交易次数: {stats.get('total_trades', 0)}")
    print(f"盈利交易: {stats.get('winning_trades', 0)}")
    print(f"亏损交易: {stats.get('losing_trades', 0)}")
    print(f"胜率: {stats.get('win_rate', 0):.2f}%")
    print(f"盈亏比: {stats.get('profit_ratio', 0):.2f}")
    print(f"利润因子: {stats.get('profit_factor', 0):.2f}")
    
    print(f"\n总盈亏: {stats.get('total_pnl', 0):,.2f}")
    print(f"总手续费: {stats.get('total_commission', 0):,.2f}")
    print(f"总滑点: {stats.get('total_slippage', 0):,.2f}")
    print(f"净盈亏: {stats.get('total_net_pnl', 0):,.2f}")


def print_chart_statistics(chart_stats):
    """打印图表统计"""
    if not chart_stats:
        return
        
    print("\n" + "="*50)
    print("图表统计")
    print("="*50)
    
    print(f"初始资金: {chart_stats.get('initial_capital', 0):,.2f}")
    print(f"最终余额: {chart_stats.get('final_balance', 0):,.2f}")
    print(f"总收益率: {chart_stats.get('total_return', 0):.2f}%")
    print(f"最大回撤: {chart_stats.get('max_drawdown', 0):.2f}%")
    print(f"数据点数: {chart_stats.get('data_points', 0)}")


def print_trade_summary(trade_summary):
    """打印交易汇总"""
    print("\n" + "="*50)
    print("交易汇总")
    print("="*50)
    
    print(f"总交易数: {trade_summary.get('total_trades', 0)}")
    print(f"盈利交易数: {trade_summary.get('winning_trades', 0)}")
    print(f"亏损交易数: {trade_summary.get('losing_trades', 0)}")
    
    # 显示最近几笔交易
    trades = trade_summary.get('trades_data', [])
    if trades:
        print(f"\n最近5笔交易:")
        for i, trade in enumerate(trades[-5:], 1):
            print(f"{i}. {trade['datetime']} {trade['direction']} {trade['offset']} "
                  f"价格:{trade['price']} 数量:{trade['volume']} 净盈亏:{trade['net_pnl']:.2f}")


def print_engine_statistics(engine_stats):
    """打印引擎统计"""
    print("\n" + "="*50)
    print("引擎统计")
    print("="*50)
    
    for engine_name, stats in engine_stats.items():
        print(f"\n{engine_name}:")
        print(f"  活跃状态: {stats.get('active', False)}")
        print(f"  初始化状态: {stats.get('initialized', False)}")
        if 'event_engine_stats' in stats:
            event_stats = stats['event_engine_stats']
            print(f"  事件处理数: {event_stats.get('event_count', 0)}")
            print(f"  错误数: {event_stats.get('error_count', 0)}")


def compare_pnl_calculations():
    """比较新旧PnL计算的差异"""
    print("\n" + "="*60)
    print("PnL计算对比 - 展示数据一致性修复")
    print("="*60)
    
    # 这里可以添加具体的对比逻辑
    print("新架构确保了以下数据的一致性:")
    print("1. strategy_log中的净盈亏计算（基准）")
    print("2. 图表引擎的资金曲线计算")
    print("3. 统计引擎的盈亏指标计算")
    print("4. 所有模块使用统一的PnLManager")


if __name__ == "__main__":
    print("VeighNa Backtester - 事件驱动架构示例")
    print("本示例展示了重构后的统一PnL计算和事件驱动架构")
    
    # 运行新架构示例
    run_new_architecture_example()
    
    # 运行兼容性示例
    run_legacy_compatibility_example()
    
    # 比较PnL计算
    compare_pnl_calculations()
    
    print("\n示例运行完成!")
    print("注意: 如果数据加载失败，请确保数据文件存在或使用实际的数据源")
