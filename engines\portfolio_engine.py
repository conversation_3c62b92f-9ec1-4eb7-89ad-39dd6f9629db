"""
Portfolio management engine for backtesting
"""

from typing import Dict, List, Optional, Tuple, Type
from datetime import datetime
import pandas as pd
import numpy as np

from vnpy_backtester.utils.constant import Direction, Offset
from vnpy_backtester.objects.object import OrderData, TradeData, PositionData, AccountData
from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.templates.template import StrategyTemplate


class PortfolioEngine:
    """
    Portfolio management engine for backtesting.
    """

    def __init__(self) -> None:
        """
        Initialize portfolio engine.
        """
        self.engines: Dict[str, BacktestingEngine] = {}
        self.strategies: Dict[str, StrategyTemplate] = {}

        self.start: datetime = None
        self.end: datetime = None

        self.capital: float = 1_000_000
        self.annual_days: int = 240

        self.daily_results: Dict[datetime, Dict] = {}
        self.daily_df: pd.DataFrame = None

    def add_engine(
        self,
        engine_name: str,
        engine: BacktestingEngine
    ) -> None:
        """
        Add backtesting engine.
        """
        self.engines[engine_name] = engine

    def add_strategy(
        self,
        strategy_name: str,
        strategy: StrategyTemplate
    ) -> None:
        """
        Add strategy.
        """
        self.strategies[strategy_name] = strategy

    def set_parameters(
        self,
        start: datetime,
        capital: float,
        annual_days: int = 240,
        end: datetime = None
    ) -> None:
        """
        Set portfolio parameters.
        """
        self.start = start
        self.capital = capital
        self.annual_days = annual_days

        if not end:
            end = datetime.now()
        self.end = end

    def run_backtesting(self) -> None:
        """
        Run backtesting for all engines.
        """
        for name, engine in self.engines.items():
            print(f"Running backtesting for {name}")
            engine.run_backtesting()

    def calculate_result(self) -> pd.DataFrame:
        """
        Calculate portfolio result.
        """
        # Calculate result for each engine
        for name, engine in self.engines.items():
            engine.calculate_result()

        # Combine daily results
        all_dates = set()
        for engine in self.engines.values():
            all_dates.update(engine.daily_results.keys())

        all_dates = sorted(all_dates)

        # Initialize portfolio daily results
        self.daily_results = {
            d: {"date": d, "balance": self.capital, "net_pnl": 0} for d in all_dates}

        # Update portfolio daily results
        for date in all_dates:
            total_pnl = 0

            for engine in self.engines.values():
                daily_result = engine.daily_results.get(date, None)
                if daily_result:
                    total_pnl += daily_result.net_pnl

            self.daily_results[date]["net_pnl"] = total_pnl

            # Update balance
            if date == all_dates[0]:
                self.daily_results[date]["balance"] = self.capital + total_pnl
            else:
                prev_date = all_dates[all_dates.index(date) - 1]
                self.daily_results[date]["balance"] = self.daily_results[prev_date]["balance"] + total_pnl

        # Calculate drawdown
        max_balance = self.capital
        for date in all_dates:
            balance = self.daily_results[date]["balance"]
            max_balance = max(max_balance, balance)
            drawdown = max_balance - balance
            drawdown_percent = drawdown / max_balance * 100

            self.daily_results[date]["drawdown"] = drawdown
            self.daily_results[date]["drawdown_percent"] = drawdown_percent

        # Convert to DataFrame
        self.daily_df = pd.DataFrame.from_dict(
            self.daily_results, orient="index")

        return self.daily_df

    def calculate_statistics(self) -> Dict:
        """
        Calculate portfolio statistics.
        """
        if self.daily_df is None:
            self.calculate_result()

        start_date = self.daily_df.index[0]
        end_date = self.daily_df.index[-1]

        total_days = len(self.daily_df)
        profit_days = len(self.daily_df[self.daily_df["net_pnl"] > 0])
        loss_days = len(self.daily_df[self.daily_df["net_pnl"] < 0])

        end_balance = self.daily_df["balance"].iloc[-1]
        max_drawdown = self.daily_df["drawdown"].max()
        max_ddpercent = self.daily_df["drawdown_percent"].max()

        total_return = (end_balance / self.capital - 1) * 100
        annual_return = total_return / total_days * self.annual_days

        daily_return = self.daily_df["net_pnl"] / self.capital
        return_std = daily_return.std() * np.sqrt(self.annual_days)
        # 不考虑无风险利率，直接使用年化收益率除以年化标准差
        sharpe_ratio = annual_return / \
            (return_std * 100) if return_std > 0 else float('inf')

        return {
            "start_date": start_date,
            "end_date": end_date,
            "total_days": total_days,
            "profit_days": profit_days,
            "loss_days": loss_days,
            "capital": self.capital,
            "end_balance": end_balance,
            "max_drawdown": max_drawdown,
            "max_ddpercent": max_ddpercent,
            "total_return": total_return,
            "annual_return": annual_return,
            "daily_return": total_return / total_days,
            "return_std": return_std,
            "sharpe_ratio": sharpe_ratio,
            "return_drawdown_ratio": (annual_return / max_ddpercent) if max_ddpercent else 0
        }

    def show_chart(self) -> None:
        """
        Show portfolio chart.
        """
        if self.daily_df is None:
            self.calculate_result()

        try:
            import matplotlib.pyplot as plt
            import seaborn as sns

            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 12))

            plt.subplot(3, 1, 1)
            plt.title("Balance")
            plt.plot(self.daily_df["balance"])

            plt.subplot(3, 1, 2)
            plt.title("Drawdown")
            plt.fill_between(
                self.daily_df.index,
                self.daily_df["drawdown_percent"],
                0,
                alpha=0.3,
                color="r"
            )

            plt.subplot(3, 1, 3)
            plt.title("Daily Pnl")
            plt.bar(self.daily_df.index, self.daily_df["net_pnl"])

            plt.tight_layout()
            plt.show()
        except:
            print("Cannot show chart, matplotlib not installed")
