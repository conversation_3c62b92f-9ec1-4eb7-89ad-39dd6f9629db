"""
PnL Manager for unified profit and loss calculation.
This module provides a centralized way to calculate and track P&L across the system.
"""

from typing import Dict, List, Tuple, Optional
from datetime import datetime
from collections import defaultdict, deque
from dataclasses import dataclass

from vnpy_backtester.utils.constant import Direction, Offset
from vnpy_backtester.objects.object import TradeData
from vnpy_backtester.utils.event import Event, EventType, EventEngine


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    direction: Direction
    price: float
    volume: float
    open_time: datetime
    
    
@dataclass
class PnLResult:
    """盈亏结果"""
    trade_pnl: float  # 交易盈亏
    commission: float  # 手续费
    slippage: float   # 滑点
    net_pnl: float    # 净盈亏
    return_rate: float  # 收益率


class PnLManager:
    """
    统一的盈亏管理器
    负责计算和跟踪所有的盈亏数据，确保系统中盈亏计算的一致性
    """
    
    def __init__(self, event_engine: EventEngine = None):
        """
        初始化盈亏管理器
        
        参数:
        event_engine: 事件引擎，用于发布盈亏更新事件
        """
        self.event_engine = event_engine
        
        # 持仓管理 - 按symbol和direction分组
        self.positions: Dict[str, Dict[Direction, deque]] = defaultdict(lambda: defaultdict(deque))
        
        # 交易记录
        self.trades: List[TradeData] = []
        
        # 盈亏统计
        self.total_pnl: float = 0.0
        self.total_commission: float = 0.0
        self.total_slippage: float = 0.0
        self.total_net_pnl: float = 0.0
        
        # 费率设置
        self.commission_rate: float = 0.0003
        self.slippage_rate: float = 0.0
        self.size: float = 1.0
        
    def set_parameters(self, commission_rate: float = None, slippage_rate: float = None, size: float = None):
        """设置交易参数"""
        if commission_rate is not None:
            self.commission_rate = commission_rate
        if slippage_rate is not None:
            self.slippage_rate = slippage_rate
        if size is not None:
            self.size = size
            
    def calculate_commission(self, trade: TradeData) -> float:
        """计算手续费"""
        return trade.price * trade.volume * self.commission_rate
        
    def calculate_slippage(self, trade: TradeData) -> float:
        """计算滑点"""
        return trade.price * trade.volume * self.slippage_rate
        
    def process_trade(self, trade: TradeData) -> PnLResult:
        """
        处理交易并计算盈亏
        
        参数:
        trade: 交易数据
        
        返回:
        PnLResult: 盈亏结果
        """
        # 记录交易
        self.trades.append(trade)
        
        # 计算手续费和滑点
        commission = self.calculate_commission(trade)
        slippage = self.calculate_slippage(trade)
        
        # 更新总手续费和滑点
        self.total_commission += commission
        self.total_slippage += slippage
        
        # 计算交易盈亏
        trade_pnl = self._calculate_trade_pnl(trade)
        
        # 计算净盈亏
        net_pnl = trade_pnl - commission - slippage
        
        # 更新总盈亏
        self.total_pnl += trade_pnl
        self.total_net_pnl += net_pnl
        
        # 计算收益率（基于开仓金额）
        open_amount = trade.price * trade.volume
        return_rate = (net_pnl / open_amount * 100) if open_amount > 0 else 0.0
        
        # 创建盈亏结果
        pnl_result = PnLResult(
            trade_pnl=trade_pnl,
            commission=commission,
            slippage=slippage,
            net_pnl=net_pnl,
            return_rate=return_rate
        )
        
        # 发布盈亏更新事件
        if self.event_engine:
            event = Event(
                type=EventType.EVENT_PNL_UPDATE,
                data={
                    "trade": trade,
                    "pnl_result": pnl_result,
                    "total_net_pnl": self.total_net_pnl
                },
                source="PnLManager"
            )
            self.event_engine.put(event)
            
        return pnl_result
        
    def _calculate_trade_pnl(self, trade: TradeData) -> float:
        """
        计算交易盈亏（不包括手续费和滑点）
        
        参数:
        trade: 交易数据
        
        返回:
        float: 交易盈亏
        """
        symbol = trade.vt_symbol
        direction = trade.direction
        offset = trade.offset
        price = trade.price
        volume = trade.volume
        
        # 开仓
        if offset == Offset.OPEN:
            # 添加到持仓
            position = Position(
                symbol=symbol,
                direction=direction,
                price=price,
                volume=volume,
                open_time=trade.datetime
            )
            self.positions[symbol][direction].append(position)
            # 开仓没有盈亏
            return 0.0
            
        # 平仓
        else:
            # 获取对手方向的持仓
            opposite_direction = Direction.SHORT if direction == Direction.LONG else Direction.LONG
            position_queue = self.positions[symbol][opposite_direction]
            
            # 如果没有持仓，返回0
            if not position_queue:
                return 0.0
                
            # 计算平仓盈亏
            remaining_volume = volume
            total_pnl = 0.0
            
            # 按照先开先平的原则计算盈亏
            while position_queue and remaining_volume > 0:
                position = position_queue[0]
                
                # 计算本次平仓的数量
                close_volume = min(remaining_volume, position.volume)
                
                # 计算盈亏
                if direction == Direction.LONG:  # 平空
                    # 开空的价格 - 平空的价格
                    pnl = (position.price - price) * close_volume * self.size
                else:  # 平多
                    # 平多的价格 - 开多的价格
                    pnl = (price - position.price) * close_volume * self.size
                    
                total_pnl += pnl
                
                # 更新剩余数量
                remaining_volume -= close_volume
                
                # 更新持仓
                if close_volume == position.volume:
                    # 如果完全平仓，移除该持仓
                    position_queue.popleft()
                else:
                    # 如果部分平仓，更新持仓数量
                    position.volume -= close_volume
                    
            return total_pnl
            
    def get_position_summary(self) -> Dict[str, Dict[str, float]]:
        """获取持仓汇总"""
        summary = {}
        for symbol, directions in self.positions.items():
            symbol_summary = {}
            for direction, position_queue in directions.items():
                total_volume = sum(pos.volume for pos in position_queue)
                if total_volume > 0:
                    avg_price = sum(pos.price * pos.volume for pos in position_queue) / total_volume
                    symbol_summary[direction.value] = {
                        "volume": total_volume,
                        "avg_price": avg_price,
                        "position_count": len(position_queue)
                    }
            if symbol_summary:
                summary[symbol] = symbol_summary
        return summary
        
    def get_statistics(self) -> Dict[str, float]:
        """获取盈亏统计"""
        return {
            "total_trades": len(self.trades),
            "total_pnl": self.total_pnl,
            "total_commission": self.total_commission,
            "total_slippage": self.total_slippage,
            "total_net_pnl": self.total_net_pnl,
            "avg_pnl_per_trade": self.total_net_pnl / len(self.trades) if self.trades else 0.0
        }
        
    def reset(self):
        """重置所有数据"""
        self.positions.clear()
        self.trades.clear()
        self.total_pnl = 0.0
        self.total_commission = 0.0
        self.total_slippage = 0.0
        self.total_net_pnl = 0.0
