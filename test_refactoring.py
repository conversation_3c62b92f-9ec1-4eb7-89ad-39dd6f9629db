"""
测试重构后的系统
验证事件驱动架构和统一PnL计算是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有新模块的导入"""
    print("测试模块导入...")
    
    try:
        # 测试事件系统
        from vnpy_backtester.utils.event import EventEngine, Event, EventType
        print("✓ 事件系统导入成功")
        
        # 测试PnL管理器
        from vnpy_backtester.utils.pnl_manager import PnLManager
        print("✓ PnL管理器导入成功")
        
        # 测试事件驱动引擎基类
        from vnpy_backtester.engines.event_driven_engine import EventDrivenEngine
        print("✓ 事件驱动引擎基类导入成功")
        
        # 测试新引擎
        from vnpy_backtester.engines.chart_engine import ChartEngine
        from vnpy_backtester.engines.statistics_engine import StatisticsEngine
        print("✓ 新引擎导入成功")
        
        # 测试回测管理器
        from vnpy_backtester.managers.backtest_manager import BacktestManager
        print("✓ 回测管理器导入成功")
        
        # 测试兼容性包装器
        from vnpy_backtester.compatibility.legacy_wrapper import BacktestingEngine, LegacyBacktestingEngine
        print("✓ 兼容性包装器导入成功")
        
        # 测试主要导入
        from vnpy_backtester import BacktestingEngine as MainBacktestingEngine
        from vnpy_backtester import BacktestManager as MainBacktestManager
        print("✓ 主要模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_engine():
    """测试事件引擎"""
    print("\n测试事件引擎...")
    
    try:
        from vnpy_backtester.utils.event import EventEngine, Event, EventType
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 测试事件处理器
        received_events = []
        
        def test_handler(event):
            received_events.append(event)
            
        # 注册处理器
        event_engine.register(EventType.EVENT_TICK, test_handler)
        
        # 启动引擎
        event_engine.start()
        
        # 发送测试事件
        test_event = Event(EventType.EVENT_TICK, data="test_data", source="test")
        event_engine.put(test_event)
        
        # 等待处理
        import time
        time.sleep(0.1)
        
        # 停止引擎
        event_engine.stop()
        
        # 验证结果
        if received_events:
            print("✓ 事件引擎工作正常")
            return True
        else:
            print("✗ 事件引擎未收到事件")
            return False
            
    except Exception as e:
        print(f"✗ 事件引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pnl_manager():
    """测试PnL管理器"""
    print("\n测试PnL管理器...")
    
    try:
        from vnpy_backtester.utils.pnl_manager import PnLManager
        from vnpy_backtester.objects.object import TradeData
        from vnpy_backtester.utils.constant import Direction, Offset, Exchange
        from datetime import datetime
        
        # 创建PnL管理器
        pnl_manager = PnLManager()
        pnl_manager.set_parameters(commission_rate=0.0003, slippage_rate=0.0, size=1.0)
        
        # 创建测试交易
        # 开多仓
        open_trade = TradeData(
            symbol="BTCUSDT",
            exchange=Exchange.BINANCE,
            orderid="1",
            tradeid="1",
            direction=Direction.LONG,
            offset=Offset.OPEN,
            price=50000.0,
            volume=1.0,
            datetime=datetime.now(),
            gateway_name="TEST"
        )
        
        # 平多仓
        close_trade = TradeData(
            symbol="BTCUSDT",
            exchange=Exchange.BINANCE,
            orderid="2",
            tradeid="2",
            direction=Direction.SHORT,
            offset=Offset.CLOSE,
            price=51000.0,
            volume=1.0,
            datetime=datetime.now(),
            gateway_name="TEST"
        )
        
        # 处理交易
        open_result = pnl_manager.process_trade(open_trade)
        close_result = pnl_manager.process_trade(close_trade)
        
        # 验证结果
        stats = pnl_manager.get_statistics()
        
        print(f"  开仓盈亏: {open_result.net_pnl}")
        print(f"  平仓盈亏: {close_result.net_pnl}")
        print(f"  总净盈亏: {stats['total_net_pnl']}")
        
        if close_result.trade_pnl > 0:  # 应该盈利1000
            print("✓ PnL管理器计算正确")
            return True
        else:
            print("✗ PnL管理器计算错误")
            return False
            
    except Exception as e:
        print(f"✗ PnL管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_legacy_compatibility():
    """测试向后兼容性"""
    print("\n测试向后兼容性...")
    
    try:
        from vnpy_backtester import BacktestingEngine
        
        # 创建引擎（应该使用兼容性包装器）
        engine = BacktestingEngine()
        
        # 验证是否是兼容性包装器
        if hasattr(engine, '_manager'):
            print("✓ 兼容性包装器工作正常")
            
            # 测试获取新管理器
            manager = engine.get_manager()
            if manager:
                print("✓ 可以访问底层新管理器")
                return True
            else:
                print("✗ 无法访问底层新管理器")
                return False
        else:
            print("✗ 兼容性包装器未正确工作")
            return False
            
    except Exception as e:
        print(f"✗ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_new_manager():
    """测试新的回测管理器"""
    print("\n测试新的回测管理器...")
    
    try:
        from vnpy_backtester.managers.backtest_manager import BacktestManager
        from vnpy_backtester.utils.constant import Interval, BacktestingMode
        
        # 创建管理器
        manager = BacktestManager()
        
        # 测试初始化
        manager.initialize(
            vt_symbol="BTCUSDT.BINANCE",
            start=datetime(2023, 1, 1),
            end=datetime(2023, 1, 2),
            rate=0.0003,
            slippage=0.0,
            size=1.0,
            pricetick=0.01,
            capital=100000,
            mode=BacktestingMode.BAR,
            interval=Interval.MINUTE
        )
        
        # 验证初始化
        if manager.initialized:
            print("✓ 回测管理器初始化成功")
            
            # 测试统计
            stats = manager.get_statistics()
            if isinstance(stats, dict):
                print("✓ 统计功能正常")
                
                # 停止管理器
                manager.stop()
                print("✓ 回测管理器测试完成")
                return True
            else:
                print("✗ 统计功能异常")
                return False
        else:
            print("✗ 回测管理器初始化失败")
            return False
            
    except Exception as e:
        print(f"✗ 回测管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_consistency():
    """测试数据一致性"""
    print("\n测试数据一致性...")
    
    try:
        from vnpy_backtester.utils.pnl_manager import PnLManager
        from vnpy_backtester.engines.statistics_engine import StatisticsEngine
        from vnpy_backtester.engines.chart_engine import ChartEngine
        from vnpy_backtester.utils.event import EventEngine
        
        # 创建事件引擎
        event_engine = EventEngine()
        event_engine.start()
        
        # 创建各个引擎
        pnl_manager = PnLManager(event_engine)
        stats_engine = StatisticsEngine(event_engine)
        chart_engine = ChartEngine(event_engine)
        
        # 初始化
        stats_engine.initialize()
        chart_engine.initialize()
        
        stats_engine.start_engine()
        chart_engine.start_engine()
        
        # 设置参数
        stats_engine.set_parameters(initial_capital=100000)
        chart_engine.set_initial_capital(100000)
        
        print("✓ 所有引擎创建成功")
        
        # 停止引擎
        stats_engine.stop_engine()
        chart_engine.stop_engine()
        event_engine.stop()
        
        print("✓ 数据一致性测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("="*60)
    print("VeighNa Backtester 重构验证测试")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("事件引擎", test_event_engine),
        ("PnL管理器", test_pnl_manager),
        ("向后兼容性", test_legacy_compatibility),
        ("新回测管理器", test_new_manager),
        ("数据一致性", test_data_consistency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed+1}/{total}] {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        print("\n重构完成的功能:")
        print("✓ 事件驱动架构")
        print("✓ 统一PnL计算")
        print("✓ 向后兼容性")
        print("✓ 新的管理器架构")
        print("✓ 数据一致性修复")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    print("="*60)


if __name__ == "__main__":
    main()
