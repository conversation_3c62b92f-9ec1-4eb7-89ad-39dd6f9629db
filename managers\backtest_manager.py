"""
Unified Backtest Manager with Event-Driven Architecture.
This module provides a centralized manager for all backtesting engines.
"""

from typing import Dict, List, Optional, Any, Type
from datetime import datetime
import pandas as pd

from vnpy_backtester.utils.event import EventEngine, Event, EventType
from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.engines.chart_engine import ChartEngine
from vnpy_backtester.engines.statistics_engine import StatisticsEngine
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.utils.constant import Interval, BacktestingMode
from vnpy_backtester.objects.object import BarData


class BacktestManager:
    """
    统一的回测管理器
    
    整合所有引擎，提供统一的回测接口，确保数据一致性
    """
    
    def __init__(self):
        """初始化回测管理器"""
        # 创建事件引擎
        self.event_engine = EventEngine()
        
        # 创建各个引擎
        self.backtesting_engine = BacktestingEngine(self.event_engine)
        self.chart_engine = ChartEngine(self.event_engine)
        self.statistics_engine = StatisticsEngine(self.event_engine)
        
        # 管理器状态
        self.initialized = False
        self.running = False
        
        # 回测配置
        self.config = {}
        
        # 注册管理器级别的事件处理器
        self._register_manager_events()
        
    def _register_manager_events(self):
        """注册管理器级别的事件处理器"""
        self.event_engine.register(EventType.EVENT_BACKTESTING_FINISHED, self._on_backtesting_finished)
        
    def initialize(self, 
                  vt_symbol: str,
                  start: datetime,
                  end: datetime,
                  rate: float = 0.0003,
                  slippage: float = 0.0,
                  size: float = 1.0,
                  pricetick: float = 0.01,
                  capital: int = 100000,
                  mode: BacktestingMode = BacktestingMode.BAR,
                  interval: Interval = Interval.MINUTE,
                  **kwargs):
        """
        初始化回测参数
        
        参数:
        vt_symbol: 交易品种
        start: 开始时间
        end: 结束时间
        rate: 手续费率
        slippage: 滑点
        size: 合约大小
        pricetick: 最小价格变动
        capital: 初始资金
        mode: 回测模式
        interval: 时间间隔
        **kwargs: 其他参数
        """
        # 保存配置
        self.config = {
            "vt_symbol": vt_symbol,
            "start": start,
            "end": end,
            "rate": rate,
            "slippage": slippage,
            "size": size,
            "pricetick": pricetick,
            "capital": capital,
            "mode": mode,
            "interval": interval,
            **kwargs
        }
        
        # 初始化回测引擎
        self.backtesting_engine.set_parameters(
            vt_symbol=vt_symbol,
            interval=interval,
            start=start,
            end=end,
            rate=rate,
            slippage=slippage,
            size=size,
            pricetick=pricetick,
            capital=capital,
            mode=mode
        )
        
        # 初始化图表引擎
        self.chart_engine.set_initial_capital(capital)
        
        # 初始化统计引擎
        self.statistics_engine.set_parameters(
            initial_capital=capital,
            annual_days=kwargs.get("annual_days", 240),
            risk_free_rate=kwargs.get("risk_free", 0.0)
        )
        
        # 启动事件引擎
        if not self.event_engine._active:
            self.event_engine.start()
            
        # 初始化各个引擎
        self.backtesting_engine.initialize()
        self.chart_engine.initialize()
        self.statistics_engine.initialize()
        
        self.initialized = True
        self.write_log("BacktestManager initialized successfully")
        
    def add_strategy(self, strategy_class: Type[StrategyTemplate], setting: Dict[str, Any]) -> str:
        """
        添加策略
        
        参数:
        strategy_class: 策略类
        setting: 策略参数
        
        返回:
        str: 策略名称
        """
        if not self.initialized:
            raise RuntimeError("BacktestManager not initialized")
            
        strategy_name = self.backtesting_engine.add_strategy(strategy_class, setting)
        self.write_log(f"Strategy added: {strategy_name}")
        return strategy_name
        
    def load_data(self, data: List[BarData] = None, file_path: str = None) -> bool:
        """
        加载数据
        
        参数:
        data: K线数据列表
        file_path: 数据文件路径
        
        返回:
        bool: 是否成功
        """
        if not self.initialized:
            raise RuntimeError("BacktestManager not initialized")
            
        try:
            if data:
                self.backtesting_engine.history_data = data
            elif file_path:
                # 从文件加载数据
                df = pd.read_csv(file_path)
                # 转换为BarData对象
                data = self._convert_df_to_bars(df)
                self.backtesting_engine.history_data = data
            else:
                # 使用引擎的数据加载功能
                self.backtesting_engine.load_data()
                
            self.write_log(f"Data loaded: {len(self.backtesting_engine.history_data)} bars")
            return True
            
        except Exception as e:
            self.write_log(f"Failed to load data: {e}")
            return False
            
    def run_backtesting(self) -> Dict[str, Any]:
        """
        运行回测
        
        返回:
        Dict[str, Any]: 回测结果
        """
        if not self.initialized:
            raise RuntimeError("BacktestManager not initialized")
            
        if self.running:
            raise RuntimeError("Backtesting is already running")
            
        try:
            self.running = True
            self.write_log("Starting backtesting...")
            
            # 发布回测开始事件
            event = Event(EventType.EVENT_BACKTESTING_START, source="BacktestManager")
            self.event_engine.put(event)
            
            # 启动各个引擎
            self.backtesting_engine.start_engine()
            self.chart_engine.start_engine()
            self.statistics_engine.start_engine()
            
            # 运行回测
            result = self.backtesting_engine.run_backtesting()
            
            # 发布回测完成事件
            event = Event(EventType.EVENT_BACKTESTING_FINISHED, data=result, source="BacktestManager")
            self.event_engine.put(event)
            
            self.write_log("Backtesting completed successfully")
            return result
            
        except Exception as e:
            self.write_log(f"Backtesting failed: {e}")
            raise
        finally:
            self.running = False
            
    def get_results(self) -> Dict[str, Any]:
        """
        获取完整的回测结果
        
        返回:
        Dict[str, Any]: 包含所有引擎结果的字典
        """
        if not self.initialized:
            raise RuntimeError("BacktestManager not initialized")
            
        # 获取统计结果
        statistics = self.statistics_engine.calculate_statistics()
        
        # 获取图表统计
        chart_stats = self.chart_engine.get_chart_statistics()
        
        # 获取交易汇总
        trade_summary = self.statistics_engine.get_trade_summary()
        
        # 获取引擎统计
        engine_stats = {
            "backtesting_engine": self.backtesting_engine.get_engine_statistics(),
            "chart_engine": self.chart_engine.get_engine_statistics(),
            "statistics_engine": self.statistics_engine.get_engine_statistics()
        }
        
        return {
            "config": self.config,
            "statistics": statistics,
            "chart_statistics": chart_stats,
            "trade_summary": trade_summary,
            "engine_statistics": engine_stats
        }
        
    def show_chart(self, title: str = None, save_path: str = None):
        """
        显示图表
        
        参数:
        title: 图表标题
        save_path: 保存路径
        """
        if title or save_path:
            self.chart_engine.set_chart_config(title=title, save_path=save_path)
        self.chart_engine.show_and_save_chart()
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计结果"""
        return self.statistics_engine.calculate_statistics()
        
    def get_trades(self) -> List[Dict[str, Any]]:
        """获取交易记录"""
        return self.statistics_engine.get_trade_summary()["trades_data"]
        
    def reset(self):
        """重置所有数据"""
        if self.running:
            raise RuntimeError("Cannot reset while backtesting is running")
            
        # 重置各个引擎
        self.chart_engine.reset_data()
        self.statistics_engine.reset_statistics()
        
        # 重置状态
        self.initialized = False
        self.config.clear()
        
        self.write_log("BacktestManager reset")
        
    def stop(self):
        """停止回测管理器"""
        if self.running:
            self.running = False
            
        # 停止各个引擎
        self.backtesting_engine.stop_engine()
        self.chart_engine.stop_engine()
        self.statistics_engine.stop_engine()
        
        # 停止事件引擎
        if self.event_engine._active:
            self.event_engine.stop()
            
        self.write_log("BacktestManager stopped")
        
    def set_debug_mode(self, debug: bool):
        """设置调试模式"""
        self.event_engine.set_debug_mode(debug)
        self.backtesting_engine.set_debug_mode(debug)
        self.chart_engine.set_debug_mode(debug)
        self.statistics_engine.set_debug_mode(debug)
        
    def _on_backtesting_finished(self, event: Event):
        """回测完成事件处理"""
        self.write_log("Received backtesting finished event")
        
    def _convert_df_to_bars(self, df: pd.DataFrame) -> List[BarData]:
        """将DataFrame转换为BarData列表"""
        # 这里需要根据实际的数据格式进行转换
        # 这是一个示例实现
        bars = []
        for _, row in df.iterrows():
            bar = BarData(
                symbol=self.config["vt_symbol"].split(".")[0],
                exchange=self.config["vt_symbol"].split(".")[1],
                datetime=pd.to_datetime(row["datetime"]),
                interval=self.config["interval"],
                volume=row.get("volume", 0),
                open_price=row["open"],
                high_price=row["high"],
                low_price=row["low"],
                close_price=row["close"],
                gateway_name="BACKTESTING"
            )
            bars.append(bar)
        return bars
        
    def write_log(self, msg: str):
        """写日志"""
        print(f"[BacktestManager] {msg}")
        
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
