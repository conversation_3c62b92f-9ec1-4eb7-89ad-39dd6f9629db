# VNPY风格量化交易回测系统

一个基于Python的高性能量化交易回测系统，参考VNPY架构设计，采用事件驱动架构。

## 项目特点

- **模块化设计**：引擎、策略、数据处理等组件高度解耦
- **高性能**：优化的数据结构和算法，支持大规模数据回测
- **灵活配置**：通过engine.set_parameters统一设置所有策略参数
- **可视化分析**：使用Plotly生成交互式回测结果图表
- **多策略支持**：支持均线交叉、多头寸等多种策略类型
- **完整交易成本**：精确计算包含开平仓手续费和滑点的净盈亏
- **详细日志记录**：自动记录交易详情，包括手续费、滑点和净盈亏

## 项目结构

```
vnpy_backtester/
├── charts/                # 图表输出目录
├── data/                  # 数据存储目录
├── engines/               # 核心引擎组件
│   ├── analyzer.py        # 分析引擎
│   ├── data_engine.py     # 数据引擎
│   ├── engine.py          # 回测引擎（核心组件）
│   ├── portfolio_engine.py # 投资组合引擎
│   ├── risk_engine.py     # 风险管理引擎
│   └── visualizer.py      # 可视化引擎
├── logs/                  # 日志输出目录
├── objects/               # 数据对象定义
│   └── object.py          # 基础数据对象（订单、交易、K线等）
├── scripts/               # 回测脚本
│   ├── run_ma_cross_strategy.py      # 均线交叉策略回测
│   └── run_multi_position_strategy.py # 多头寸信号策略回测
├── strategies/            # 策略实现
│   ├── ma_cross_position_strategy.py # 均线交叉头寸策略
│   └── multi_position_strategy.py    # 多头寸信号策略
├── templates/             # 模板文件
│   └── template.py        # 策略模板（所有策略的基类）
├── utils/                 # 工具类
│   ├── array_manager.py   # 数组管理器（高效计算技术指标）
│   ├── base.py            # 基础类定义（回测结果、日结算等）
│   ├── chart_engine.py    # 图表引擎（Plotly可视化）
│   ├── constant.py        # 常量定义（方向、开平、交易所等）
│   ├── log_engine.py      # 日志引擎（记录交易和策略日志）
│   └── utility.py         # 工具函数（统计计算、数据处理等）
└── __init__.py            # 包初始化文件
```

## 主要功能

### 回测引擎

回测引擎是系统的核心组件，负责加载数据、执行策略、模拟交易和计算结果。主要特点：

- 支持K线级别回测
- 实现了订单撮合机制
- 支持限价单交易
- 提供详细的回测结果统计

### 策略实现

系统内置了两种交易策略：

1. **均线交叉策略**：基于快速和慢速移动平均线的交叉信号进行交易
2. **多头寸策略**：基于外部信号，支持同时持有多个交易头寸

### 数据处理

- **ArrayManager**：高效的K线数据管理器，支持各种技术指标计算
- **数据引擎**：负责加载和预处理回测数据

### 可视化分析

使用Plotly生成交互式图表，包括：

- 权益曲线
- 回撤分析
- 交易记录可视化
- 各种性能指标展示

## 快速开始

### 运行均线交叉策略回测

```python
from vnpy_backtester.scripts.run_ma_cross_strategy import run_ma_cross_strategy_backtest
import pandas as pd

# 加载数据
df = pd.read_csv('example/回测结果250411_1.csv')
df = df.set_index('Unnamed: 0').rename_axis('open_time', axis=0)
df.index = pd.to_datetime(df.index)

# 运行回测
run_ma_cross_strategy_backtest(
    df=df,
    fast_window=5,        # 快速均线周期
    slow_window=60,       # 慢速均线周期
    position_size=10,     # 每次开仓数量
    rate=0.0003,          # 手续费率
    slippage=0.001,       # 滑点
    capital=50000,        # 初始资金
    plot_show=True,       # 是否显示图表
    plot_save=True,       # 是否保存图表
    debug=False,          # 是否开启调试模式
    order_type="quantity" # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)
)
```

### 运行多头寸信号策略回测

```python
from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest
import pandas as pd

# 加载数据
df = pd.read_csv('example/回测结果250411_1.csv')
df = df.set_index('Unnamed: 0').rename_axis('open_time', axis=0)
df.index = pd.to_datetime(df.index)

# 添加信号
df['signals'] = 0
df.loc[df.index[::10], 'signals'] = 1
df.loc[df.index[5::10], 'signals'] = -1

# 运行回测
run_multi_position_strategy_backtest(
    df=df,
    holding_bars=96,      # 持仓K线数量
    position_size=1,      # 每次开仓数量
    rate=0.0003,          # 手续费率
    slippage=0.01,        # 滑点
    capital=50000,        # 初始资金
    plot_show=True,       # 是否显示图表
    plot_save=True,       # 是否保存图表
    order_type="quantity" # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)
)
```

## DataFrame要求

传入的DataFrame必须包含以下列:
- open: float类型，开盘价
- high: float类型，最高价
- low: float类型，最低价
- close: float类型，收盘价
- volume: float类型，成交量
- signals: int或float类型（仅多头寸策略需要，均线交叉策略不需要），交易信号，1表示做多，-1表示做空，0表示不操作

DataFrame索引要求:
- 索引应为datetime类型，表示每个K线的时间
- 如果索引不是datetime类型，函数会尝试使用'datetime'列作为索引
- 如果没有'datetime'列，会创建一个简单的日期索引

示例DataFrame格式:

```
                    open    high    low     close   volume  signals
2021-01-01 00:00:00 100.0   101.0   99.0    100.5   1000.0  0
2021-01-01 00:15:00 100.5   102.0   100.0   101.5   1200.0  1
2021-01-01 00:30:00 101.5   101.8   100.8   101.0   800.0   -1
...
```

## 策略说明

### 1. 均线交叉头寸策略 (MACrossPositionStrategy)

基于快速和慢速移动平均线的交叉信号进行交易，包含完整的资金管理功能:
- 当快速均线上穿慢速均线时做多
- 当快速均线下穿慢速均线时做空
- 当信号反转时平仓并反向开仓
- 检查可用资金，资金不足时不开仓并记录到日志
- 详细记录交易信息，包括开平仓手续费、滑点和净盈亏
- 支持通过engine.set_parameters设置策略参数

### 2. 多头寸信号策略 (MultiPositionStrategy)

根据signals列的值进行交易，支持同时持有多个独立仓位:
- 当signals=1时加多
- 当signals=-1时加空
- 每个仓位持有指定K线数后平仓
- 每个仓位单独记录，可以同时持有多个多单和空单
- 检查可用资金，资金不足时不开仓并记录到日志
- 在最后N根K线不开新仓，避免无法完成持仓周期
- 详细记录交易信息，包括开平仓手续费、滑点和净盈亏
- 支持通过engine.set_parameters设置策略参数

## 性能优化

系统在设计时考虑了性能优化：

- 使用高效的数据结构存储K线数据
- 优化的技术指标计算算法
- 批量处理订单以提高回测速度
- 缓存机制减少重复计算
- 优化的ArrayManager实现，提高计算效率

## 日志记录

策略的所有日志都会保存到`logs/strategy_log.log`文件中，包括:
- 策略初始化、启动和停止信息
- 开仓信息（价格、数量、手续费、滑点）
- 平仓信息（价格、数量、持仓时间、盈亏、开平仓手续费、滑点、净盈亏）
- 资金不足信息
- 盈亏信息（包含完整的交易成本计算）

## 图表显示

回测结果会生成HTML格式的交互式图表，保存在`charts/`文件夹中，包括:
- 资金曲线
- 收益率曲线
- 回撤曲线
- 交易记录标记

## 开发自定义策略

1. 继承 `StrategyTemplate` 类
2. 定义策略参数和变量
3. 实现 `on_init`、`on_bar` 等方法
4. 使用 `buy`、`sell`、`short`、`cover` 等方法执行交易
5. 使用 `write_log` 记录交易信息

## 按金额下单功能

系统支持两种下单方式：
1. 按币数量下单（默认）：每次交易固定数量的币
2. 按金额下单：每次交易固定金额的币

### 设置下单方式

在策略中设置下单方式：
```python
# 在策略类中设置
class MyStrategy(StrategyTemplate):
    order_type = "quantity"  # 按币数量下单
    position_size = 10       # 每次开仓10个币

    # 或者
    order_type = "amount"    # 按金额下单
    position_size = 1000     # 每次开仓1000元
```

在回测引擎中设置下单方式：
```python
engine.set_parameters(
    # 其他参数...
    order_type="quantity",  # 按币数量下单
    position_size=10        # 每次开仓10个币
)

# 或者
engine.set_parameters(
    # 其他参数...
    order_type="amount",    # 按金额下单
    position_size=1000      # 每次开仓1000元
)
```

### 使用下单方法

系统提供了灵活的下单方法，支持多种参数组合：

```python
# 使用策略默认设置下单
self.buy(price)  # 使用策略中的order_type和position_size

# 显式指定数量下单
self.buy(price, volume=10)  # 买入10个币

# 显式指定金额下单
self.buy(price, amount=1000)  # 买入价值1000元的币
```

所有交易方法（buy、sell、short、cover）都支持这些参数组合。

示例：
```python
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.objects.object import BarData

class MyStrategy(StrategyTemplate):
    # 定义策略参数
    param1 = 10
    param2 = 20
    position_size = 1      # 每次开仓的数量或金额
    order_type = "quantity"  # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)

    # 定义策略变量
    var1 = 0

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 从设置中获取参数
        if "param1" in self.setting:
            self.param1 = self.setting["param1"]
        if "param2" in self.setting:
            self.param2 = self.setting["param2"]
        if "position_size" in self.setting:
            self.position_size = self.setting["position_size"]
        if "order_type" in self.setting:
            self.order_type = self.setting["order_type"]

        # 记录下单方式和仓位大小
        if self.order_type == "quantity":
            self.write_log(f"下单方式: 按币数量, 每次开仓数量: {self.position_size}")
        else:  # order_type == "amount"
            self.write_log(f"下单方式: 按金额, 每次开仓金额: {self.position_size}")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size

    def on_init(self):
        """策略初始化"""
        self.write_log("策略初始化")

    def on_bar(self, bar: BarData):
        """K线更新回调"""
        # 交易逻辑
        if your_condition:
            # 根据order_type自动选择按数量或按金额下单
            self.buy(bar.close_price)  # 使用默认的position_size和order_type

            # 也可以显式指定数量或金额
            # self.buy(bar.close_price, volume=10)  # 按数量下单，买入10个币
            # self.buy(bar.close_price, amount=1000)  # 按金额下单，买入价值1000元的币

            if self.order_type == "quantity":
                self.write_log(f"做多(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{self.position_size}")
            else:
                self.write_log(f"做多(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}")
```

使用自定义策略进行回测：
```python
from vnpy_backtester.engines.engine import BacktestingEngine
from my_strategy import MyStrategy
import pandas as pd

# 创建回测引擎
engine = BacktestingEngine()

# 设置回测参数
engine.set_parameters(
    vt_symbol="BTCUSDT.BINANCE",
    start=start_date,
    end=end_date,
    rate=0.0003,          # 手续费率
    slippage=0.01,        # 滑点
    capital=100000,       # 初始资金
    param1=15,            # 自定义策略参数
    param2=30,            # 自定义策略参数
    position_size=10,     # 每次开仓的数量或金额
    order_type="quantity" # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)
)

# 添加策略
engine.add_strategy(MyStrategy)

# 加载数据
df = pd.read_csv('example/data.csv')
df = df.set_index('open_time')
df.index = pd.to_datetime(df.index)
engine.history_data = engine.load_data_from_dataframe(df)

# 运行回测
engine.run_backtesting()

# 计算结果
engine.calculate_result()
engine.calculate_statistics()

# 显示结果
engine.show_result()

# 绘制图表
engine.plot_result(plot_show=True, plot_save=True)

# 查看交易日志
# 交易记录保存在 logs/trade_log.log
# 策略日志保存在 logs/strategy_log.log
```

回测完成后，您可以查看两种日志文件：
1. `logs/strategy_log.log` - 包含策略运行过程中的详细日志
2. `logs/trade_log.log` - 包含基于事件驱动自动记录的所有交易详情

## 贡献者

- cxc (<EMAIL>)

## 许可证

私有项目，未指定开源许可证
